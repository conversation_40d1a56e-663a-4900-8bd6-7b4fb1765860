package com.sdesrd.filetransfer.server.util;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * 文件工具类测试
 */
@DisplayName("文件工具类测试")
class FileUtilsTest {
    
    @TempDir
    Path tempDir;
    
    private File testFile;
    
    @BeforeEach
    void setUp() throws IOException {
        // 创建测试文件
        testFile = createTestFile("test-file.txt", "这是一个测试文件内容，用于验证文件工具类功能。");
    }
    
    @Test
    @DisplayName("文件MD5计算测试")
    void testCalculateFileMD5() throws IOException {
        // 计算MD5
        String md5 = FileUtils.calculateFileMD5(testFile);
        
        // 验证MD5格式
        assertNotNull(md5);
        assertEquals(32, md5.length()); // MD5应该是32位十六进制字符串
        assertTrue(md5.matches("[a-fA-F0-9]{32}")); // 验证是否为有效的十六进制
        
        // 验证相同内容的文件有相同的MD5
        File testFile2 = createTestFile("test-file2.txt", "这是一个测试文件内容，用于验证文件工具类功能。");
        String md5_2 = FileUtils.calculateFileMD5(testFile2);
        assertEquals(md5, md5_2);
        
        // 验证不同内容的文件有不同的MD5
        File testFile3 = createTestFile("test-file3.txt", "这是不同的文件内容。");
        String md5_3 = FileUtils.calculateFileMD5(testFile3);
        assertNotEquals(md5, md5_3);
    }
    
    @Test
    @DisplayName("文件复制测试")
    void testCopyFile() throws IOException {
        // 创建目标文件路径
        String targetPath = tempDir.resolve("copied-file.txt").toString();
        
        // 执行文件复制
        FileUtils.copyFile(testFile.getAbsolutePath(), targetPath);
        
        // 验证复制结果
        File targetFile = new File(targetPath);
        assertTrue(targetFile.exists());
        assertEquals(testFile.length(), targetFile.length());
        
        // 验证文件内容一致性
        String originalMd5 = FileUtils.calculateFileMD5(testFile);
        String copiedMd5 = FileUtils.calculateFileMD5(targetFile);
        assertEquals(originalMd5, copiedMd5);
    }
    
    @Test
    @DisplayName("文件大小格式化测试")
    void testFormatFileSize() {
        // 测试字节
        assertEquals("100 B", FileUtils.formatFileSize(100));
        
        // 测试KB
        assertEquals("1.0 KB", FileUtils.formatFileSize(1024));
        assertEquals("1.5 KB", FileUtils.formatFileSize(1536));
        
        // 测试MB
        assertEquals("1.0 MB", FileUtils.formatFileSize(1024 * 1024));
        assertEquals("2.5 MB", FileUtils.formatFileSize((long)(2.5 * 1024 * 1024)));
        
        // 测试GB
        assertEquals("1.0 GB", FileUtils.formatFileSize(1024L * 1024 * 1024));
        
        // 测试TB
        assertEquals("1.0 TB", FileUtils.formatFileSize(1024L * 1024 * 1024 * 1024));
    }
    
    @Test
    @DisplayName("字节数组MD5计算测试")
    void testCalculateMD5ByteArray() {
        // 测试字节数组MD5计算
        byte[] data = "Hello, World!".getBytes();
        String md5 = FileUtils.calculateMD5(data);

        // 验证MD5格式
        assertNotNull(md5);
        assertEquals(32, md5.length());
        assertTrue(md5.matches("[a-fA-F0-9]{32}"));

        // 验证相同数据产生相同MD5
        byte[] data2 = "Hello, World!".getBytes();
        String md5_2 = FileUtils.calculateMD5(data2);
        assertEquals(md5, md5_2);

        // 验证不同数据产生不同MD5
        byte[] data3 = "Hello, World! Different".getBytes();
        String md5_3 = FileUtils.calculateMD5(data3);
        assertNotEquals(md5, md5_3);
    }
    
    @Test
    @DisplayName("文件大小格式化边界测试")
    void testFormatFileSizeBoundary() {
        // 测试边界值
        assertEquals("0 B", FileUtils.formatFileSize(0));
        assertEquals("1 B", FileUtils.formatFileSize(1));
        assertEquals("1023 B", FileUtils.formatFileSize(1023));
        assertEquals("1.0 KB", FileUtils.formatFileSize(1024));
        assertEquals("1.5 KB", FileUtils.formatFileSize(1536));

        // 测试负数
        assertEquals("0 B", FileUtils.formatFileSize(-100));

        // 测试大数值
        long oneGB = 1024L * 1024 * 1024;
        assertEquals("1.0 GB", FileUtils.formatFileSize(oneGB));

        long oneTB = 1024L * 1024 * 1024 * 1024;
        assertEquals("1.0 TB", FileUtils.formatFileSize(oneTB));
    }
    
    @Test
    @DisplayName("文件复制异常测试")
    void testCopyFileExceptions() {
        // 测试源文件不存在的情况
        assertThrows(IOException.class, () -> {
            FileUtils.copyFile("/path/to/nonexistent/source.txt",
                    tempDir.resolve("target.txt").toString());
        });
    }
    
    @Test
    @DisplayName("空文件处理测试")
    void testEmptyFileHandling() throws IOException {
        // 创建空文件
        File emptyFile = createTestFile("empty-file.txt", "");
        assertEquals(0, emptyFile.length());

        // 计算空文件的MD5
        String md5 = FileUtils.calculateFileMD5(emptyFile);
        assertNotNull(md5);
        assertEquals(32, md5.length());

        // 空文件的MD5应该是固定值
        assertEquals("d41d8cd98f00b204e9800998ecf8427e", md5.toLowerCase());
    }

    @Test
    @DisplayName("文件MD5计算异常测试")
    void testCalculateFileMD5Exception() {
        // 测试不存在的文件
        File nonExistentFile = new File("/path/to/nonexistent/file.txt");
        assertThrows(IOException.class, () -> {
            FileUtils.calculateFileMD5(nonExistentFile);
        });
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, String content) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        Files.write(file.toPath(), content.getBytes("UTF-8"));
        return file;
    }
}
