package com.sdesrd.filetransfer.client;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.client.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.client.dto.UploadResult;

/**
 * 上传结果API改进测试
 * 验证UploadResult和FileUploadInitResponse新增字段的功能
 */
public class UploadResultApiImprovementTest {

    @Test
    @DisplayName("UploadResult新增字段测试")
    void testUploadResultNewFields() {
        // 创建上传结果对象
        UploadResult result = new UploadResult();
        
        // 设置基础字段
        result.setSuccess(true);
        result.setTransferId("test-transfer-id");
        result.setFileId("01JYRCQZJAYM87T42CSJZE6VEB");
        result.setFileName("test-file.txt");
        result.setFileSize(10240L);
        result.setFastUpload(true);
        
        // 设置新增的字段
        result.setRelativePath("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt");
        result.setFileMd5("3699b11b956ae9471fb8961640b55c1c");
        result.setCompleteTime("2024-12-27T10:00:00Z");
        result.setTransferDuration(150L);
        
        // 验证基础字段
        assertTrue(result.isSuccess());
        assertEquals("test-transfer-id", result.getTransferId());
        assertEquals("01JYRCQZJAYM87T42CSJZE6VEB", result.getFileId());
        assertEquals("test-file.txt", result.getFileName());
        assertEquals(10240L, result.getFileSize());
        assertTrue(result.isFastUpload());
        
        // 验证新增字段
        assertEquals("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt", 
                    result.getRelativePath());
        assertEquals("3699b11b956ae9471fb8961640b55c1c", result.getFileMd5());
        assertEquals("2024-12-27T10:00:00Z", result.getCompleteTime());
        assertEquals(150L, result.getTransferDuration());
    }
    
    @Test
    @DisplayName("客户端FileUploadInitResponse新增字段测试")
    void testClientFileUploadInitResponseNewFields() {
        // 创建客户端的初始化响应对象
        FileUploadInitResponse response = new FileUploadInitResponse();
        
        // 设置基础字段
        response.setTransferId("test-transfer-id");
        response.setFileId("01JYRCQZJAYM87T42CSJZE6VEB");
        response.setFileName("test-file.txt");
        response.setFastUpload(true);
        response.setChunkSize(2048L);
        response.setTotalChunks(5);
        
        // 设置新增的字段（秒传时的完整信息）
        response.setRelativePath("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt");
        response.setFileMd5("3699b11b956ae9471fb8961640b55c1c");
        response.setCompleteTime("2024-12-27T10:00:00Z");
        response.setFileSize(10240L);
        response.setTransferDuration(150L);
        
        // 验证基础字段
        assertEquals("test-transfer-id", response.getTransferId());
        assertEquals("01JYRCQZJAYM87T42CSJZE6VEB", response.getFileId());
        assertEquals("test-file.txt", response.getFileName());
        assertTrue(response.getFastUpload());
        assertEquals(2048L, response.getChunkSize());
        assertEquals(5, response.getTotalChunks());
        
        // 验证新增字段
        assertEquals("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt", 
                    response.getRelativePath());
        assertEquals("3699b11b956ae9471fb8961640b55c1c", response.getFileMd5());
        assertEquals("2024-12-27T10:00:00Z", response.getCompleteTime());
        assertEquals(10240L, response.getFileSize());
        assertEquals(150L, response.getTransferDuration());
    }
    
    @Test
    @DisplayName("模拟秒传流程：从init响应构建UploadResult")
    void testFastUploadFlowFromInitToResult() {
        // 模拟服务端返回的秒传init响应
        FileUploadInitResponse initResponse = new FileUploadInitResponse();
        initResponse.setTransferId("fast-upload-transfer-id");
        initResponse.setFileId("01JYRCQZJAYM87T42CSJZE6VEB");
        initResponse.setFileName("fast-upload-file.txt");
        initResponse.setFastUpload(true);
        initResponse.setChunkSize(2048L);
        initResponse.setTotalChunks(5);
        
        // 秒传时的完整信息
        initResponse.setRelativePath("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt");
        initResponse.setFileMd5("3699b11b956ae9471fb8961640b55c1c");
        initResponse.setCompleteTime("2024-12-27T10:00:00Z");
        initResponse.setFileSize(10240L);
        initResponse.setTransferDuration(150L);
        
        // 模拟客户端从init响应构建UploadResult
        UploadResult result = new UploadResult();
        result.setSuccess(true);
        result.setTransferId(initResponse.getTransferId());
        result.setFileId(initResponse.getFileId());
        result.setFileName(initResponse.getFileName());
        result.setFileSize(10240L); // 客户端本地文件大小
        result.setFastUpload(true);
        
        // 从init响应中获取完整的文件信息
        result.setRelativePath(initResponse.getRelativePath());
        result.setFileMd5(initResponse.getFileMd5());
        result.setCompleteTime(initResponse.getCompleteTime());
        result.setTransferDuration(initResponse.getTransferDuration());
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.isFastUpload());
        assertEquals("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt", 
                    result.getRelativePath());
        assertEquals("3699b11b956ae9471fb8961640b55c1c", result.getFileMd5());
        assertEquals("2024-12-27T10:00:00Z", result.getCompleteTime());
        assertEquals(150L, result.getTransferDuration());
    }
}
