# ================================================================================
# 文件传输SDK简化构建和测试脚本（PowerShell版本）
# ================================================================================

param(
    [string]$Mode = "build-test",
    [string]$JavaHome = "",
    [string]$JavaVersion = "",
    [switch]$Help
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# ==================== 常量定义 ====================

# 脚本版本信息
$SCRIPT_VERSION = "1.0.0"
$SCRIPT_NAME = "文件传输SDK多版本构建和测试脚本"

# 项目模块列表
$PROJECT_MODULES = @(
    "file-transfer-server-sdk",
    "file-transfer-client-sdk", 
    "file-transfer-client-demo"
)

# 支持的Java版本列表
$SUPPORTED_JAVA_VERSIONS = @(
    "java8",
    "java11",
    "java17",
    "java21"
)

# 需要单元测试的模块列表
$TEST_MODULES = @(
    "file-transfer-server-sdk",
    "file-transfer-client-sdk"
)

# 核心SDK模块列表（需要安装到.m2仓库的模块）
$CORE_SDK_MODULES = @(
    "file-transfer-server-sdk",
    "file-transfer-client-sdk"
)

# 演示模块列表
$DEMO_MODULES = @("file-transfer-client-demo")

# 独立服务模块
$STANDALONE_MODULE = "file-transfer-server-standalone"

# 超时配置常量（秒）
$BUILD_TIMEOUT_SECONDS = 600
$TEST_TIMEOUT_SECONDS = 1200
$SERVER_STARTUP_TIMEOUT_SECONDS = 30
$SERVER_SHUTDOWN_TIMEOUT_SECONDS = 15
$DEMO_TEST_TIMEOUT_SECONDS = 300

# 端口配置
$TEST_SERVER_PORT = 49011

# 目录配置
$LOG_DIR = ".\logs"
$MAIN_LOG = "$LOG_DIR\build-and-test-$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
$SERVER_PID_FILE = "$LOG_DIR\test-server.pid"

# 执行模式常量
$MODE_BUILD = "build"
$MODE_BUILD_TEST = "build-test"

# ==================== 日志函数 ====================

function Write-InfoLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[INFO] $timestamp - $Message" -ForegroundColor Blue
    Add-Content -Path $MAIN_LOG -Value "[INFO] $timestamp - $Message"
}

function Write-SuccessLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[SUCCESS] $timestamp - $Message" -ForegroundColor Green
    Add-Content -Path $MAIN_LOG -Value "[SUCCESS] $timestamp - $Message"
}

function Write-WarningLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[WARNING] $timestamp - $Message" -ForegroundColor Yellow
    Add-Content -Path $MAIN_LOG -Value "[WARNING] $timestamp - $Message"
}

function Write-ErrorLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[ERROR] $timestamp - $Message" -ForegroundColor Red
    Add-Content -Path $MAIN_LOG -Value "[ERROR] $timestamp - $Message"
}

function Write-StepLog {
    param([int]$StepNumber, [string]$StepName)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[STEP $StepNumber] $timestamp - $StepName" -ForegroundColor Magenta
    Add-Content -Path $MAIN_LOG -Value "[STEP $StepNumber] $timestamp - $StepName"
    Add-Content -Path $MAIN_LOG -Value "========================================"
}

function Write-TestPhaseLog {
    param([string]$PhaseName)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[TEST_PHASE] $timestamp - $PhaseName" -ForegroundColor Cyan
    Add-Content -Path $MAIN_LOG -Value "[TEST_PHASE] $timestamp - $PhaseName"
    Add-Content -Path $MAIN_LOG -Value "----------------------------------------"
}

# ==================== 工具函数 ====================

function Initialize-Logging {
    if (!(Test-Path $LOG_DIR)) {
        New-Item -ItemType Directory -Path $LOG_DIR -Force | Out-Null
    }
    New-Item -ItemType File -Path $MAIN_LOG -Force | Out-Null
    Write-InfoLog "主日志文件：$MAIN_LOG"
}

function Show-Header {
    Write-Host "========================================================" -ForegroundColor White
    Write-Host "    $SCRIPT_NAME" -ForegroundColor White
    Write-Host "    版本：$SCRIPT_VERSION" -ForegroundColor White
    Write-Host "    时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host "========================================================" -ForegroundColor White
}

function Test-Command {
    param([string]$Command, [string]$Description)
    
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        Write-ErrorLog "$Description 未安装或未在PATH中：$Command"
        return $false
    }
}

function Test-PortAvailable {
    param([int]$Port)
    
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $Port -InformationLevel Quiet -WarningAction SilentlyContinue
        return !$connection
    } catch {
        return $true
    }
}

# ==================== 环境检查函数 ====================

# JDK结构修复功能已移除 - 现在支持使用更高版本JDK进行编译

function Setup-JavaEnvironment {
    param([string]$CustomJavaHome)
    
    Write-StepLog 1 "设置Java环境"
    
    if ($CustomJavaHome) {
        if ((Test-Path $CustomJavaHome) -and (Test-Path "$CustomJavaHome\bin\java.exe")) {
            $env:JAVA_HOME = $CustomJavaHome
            $env:PATH = "`"$CustomJavaHome\bin`";$env:PATH"
            $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            # 不设置-Djava.home参数，让Maven使用系统Java环境
            
            Write-InfoLog "使用指定的Java JDK：$CustomJavaHome"
        } else {
            Write-ErrorLog "指定的Java JDK路径无效：$CustomJavaHome"
            return $false
        }
    } else {
        # 使用系统JAVA_HOME环境变量
        if ($env:JAVA_HOME -and (Test-Path "$env:JAVA_HOME\bin\java.exe")) {
            Write-InfoLog "使用系统JAVA_HOME环境变量：$env:JAVA_HOME"
            $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            # 不设置-Djava.home参数，让Maven使用系统Java环境
            
            # 检查是否有javadoc命令
            if (!(Test-Path "$env:JAVA_HOME\bin\javadoc.exe")) {
                Write-InfoLog "javadoc命令不可用，将跳过Javadoc生成"
                $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
            }
        } else {
            Write-InfoLog "JAVA_HOME未设置或无效，使用系统默认Java环境"
            # 设置基本的Maven选项，让Maven使用PATH中的Java
            $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
        }
    }
    
    if (!(Test-Command "java" "Java运行时")) {
        return $false
    }
    
    try {
        $javaVersionOutput = & java -version 2>&1
        $javaVersion = $javaVersionOutput[0] -replace '^.*\"(.*)\".*$', '$1'
        Write-InfoLog "当前Java版本：$javaVersion"
        
        if ($javaVersion -match "1\.8\.") {
            Write-SuccessLog "使用Java 8，完全兼容"
            Write-WarningLog "建议升级到更高版本的JDK（如Java 11/17/21）以获得更好的性能和安全性"
        } elseif ($javaVersion -match "11\.") {
            Write-SuccessLog "使用Java 11，推荐版本，具有良好的性能和稳定性"
        } elseif ($javaVersion -match "17\.") {
            Write-SuccessLog "使用Java 17，长期支持版本，推荐用于生产环境"
        } elseif ($javaVersion -match "21\.") {
            Write-SuccessLog "使用Java 21，最新长期支持版本，具有最新特性和优化"
        } else {
            Write-WarningLog "当前Java版本：$javaVersion，可能存在兼容性问题"
            Write-InfoLog "建议使用Java 11、17或21等长期支持版本"
        }
    } catch {
        Write-InfoLog "Java命令可用，但无法解析版本信息"
    }
    
    return $true
}

function Test-MavenEnvironment {
    Write-StepLog 2 "检查Maven环境"
    
    if (!(Test-Command "mvn" "Apache Maven")) {
        return $false
    }
    
    try {
        $mavenVersion = & mvn -version | Select-Object -First 1
        Write-InfoLog "Maven版本：$mavenVersion"
    } catch {
        Write-WarningLog "无法获取Maven版本信息"
    }
    
    if (!$env:MAVEN_OPTS) {
        Write-InfoLog "MAVEN_OPTS未设置，使用默认配置"
        $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
        
        if ($env:JAVA_HOME) {
            # 不设置-Djava.home参数，让Maven使用系统Java环境
            Write-InfoLog "Maven配置使用Java：$env:JAVA_HOME"
        }
        
        if ($env:JAVA_HOME -and !(Test-Path "$env:JAVA_HOME\bin\javadoc.exe")) {
            Write-WarningLog "javadoc命令不可用，将跳过Javadoc生成"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
        } elseif (!$env:JAVA_HOME) {
            Write-WarningLog "JAVA_HOME未设置，将跳过Javadoc生成"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
        }
    } else {
        Write-InfoLog "使用已设置的MAVEN_OPTS"
    }
    
    Write-InfoLog "Maven选项：$env:MAVEN_OPTS"
    Write-SuccessLog "Maven环境检查完成"
    
    return $true
}

function Test-ProjectStructure {
    Write-StepLog 3 "验证项目结构"
    
    if (!(Test-Path "pom.xml")) {
        Write-ErrorLog "根目录pom.xml文件不存在"
        return $false
    }
    Write-InfoLog "根目录pom.xml文件存在"
    
    $missingModules = @()
    foreach ($module in $PROJECT_MODULES) {
        if (!(Test-Path $module)) {
            $missingModules += $module
        } else {
            Write-InfoLog "模块目录存在：$module"
            
            if (!(Test-Path "$module\pom.xml")) {
                Write-WarningLog "模块pom.xml不存在：$module\pom.xml"
            }
        }
    }
    
    if (!(Test-Path $STANDALONE_MODULE)) {
        Write-WarningLog "独立服务模块不存在：$STANDALONE_MODULE"
        Write-WarningLog "在build-test模式下将无法启动测试服务器"
    } else {
        Write-InfoLog "独立服务模块存在：$STANDALONE_MODULE"
    }
    
    if ($missingModules.Count -gt 0) {
        Write-WarningLog "以下模块目录不存在：$($missingModules -join ', ')"
        Write-WarningLog "将跳过这些模块的编译"
    }
    
    Write-SuccessLog "项目结构验证完成"
    return $true
}

function Clear-Environment {
    Write-StepLog 4 "清理构建和测试环境"
    
    Write-InfoLog "清理Maven构建缓存..."
    
    # 先停止所有可能的测试服务器进程
    Stop-AllTestServers
    
    # 等待进程完全结束
    Start-Sleep -Seconds 3
    
    if (Test-Path "target") {
        try {
            Remove-Item "target" -Recurse -Force
            Write-InfoLog "清理根目录target目录"
        } catch {
            Write-WarningLog "清理根目录target目录时发生错误：$($_.Exception.Message)"
        }
    }
    
    foreach ($module in $PROJECT_MODULES) {
        if ((Test-Path $module) -and (Test-Path "$module\target")) {
            try {
                Remove-Item "$module\target" -Recurse -Force
                Write-InfoLog "清理模块target目录：$module"
            } catch {
                Write-WarningLog "清理模块 $module target目录时发生错误：$($_.Exception.Message)"
            }
        }
    }
    
    if ((Test-Path $STANDALONE_MODULE) -and (Test-Path "$STANDALONE_MODULE\target")) {
        try {
            Remove-Item "$STANDALONE_MODULE\target" -Recurse -Force
            Write-InfoLog "清理独立服务模块target目录：$STANDALONE_MODULE"
        } catch {
            Write-WarningLog "清理独立服务模块target目录时发生错误：$($_.Exception.Message)"
            Write-InfoLog "文件可能被进程占用，跳过此目录的清理"
        }
    }
    
    $testDataDirs = @(".\test-data", ".\data", ".\$STANDALONE_MODULE\data")
    foreach ($dir in $testDataDirs) {
        if (Test-Path $dir) {
            Remove-Item $dir -Recurse -Force
            Write-InfoLog "清理测试数据目录：$dir"
        }
    }
    
    Get-ChildItem -Recurse -Filter "*.tmp" | Remove-Item -Force 2>$null
    Get-ChildItem -Recurse -Filter "test-*.dat" | Remove-Item -Force 2>$null
    Get-ChildItem -Recurse -Filter "*.test" | Remove-Item -Force 2>$null
    
    Stop-AllTestServers
    
    Write-SuccessLog "环境清理完成"
    return $true
}

function Stop-AllTestServers {
    Write-InfoLog "停止所有可能运行的测试服务器进程..."
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    ForEach-Object { Get-Process -Id $_ -ErrorAction SilentlyContinue }
        
        if ($processes) {
            Write-InfoLog "停止占用端口 $TEST_SERVER_PORT 的进程：$($processes.Id -join ', ')"
            $processes | Stop-Process -Force
            Start-Sleep -Seconds 2
        }
    } catch {
        # 忽略错误
    }
    
    if (Test-Path $SERVER_PID_FILE) {
        Remove-Item $SERVER_PID_FILE -Force
        Write-InfoLog "清理服务器PID文件"
    }
}

# ==================== Java版本验证函数 ====================

function Test-JavaVersion {
    param([string]$JavaVersion)
    
    if (!$JavaVersion) {
        return $true  # 允许空值（使用默认）
    }
    
    if ($SUPPORTED_JAVA_VERSIONS -contains $JavaVersion) {
        return $true
    }
    
    Write-ErrorLog "不支持的Java版本: $JavaVersion"
    Write-ErrorLog "支持的版本: $($SUPPORTED_JAVA_VERSIONS -join ', ')"
    return $false
}

# ==================== 构建功能函数 ====================

function Invoke-ProjectCompile {
    param([string]$JavaVersionProfile)
    
    Write-StepLog 5 "编译项目 (Java版本：$(if ($JavaVersionProfile) { $JavaVersionProfile } else { '默认' }))"
    
    $startTime = Get-Date
    
    # 构建Maven命令参数
    $mavenArgs = @("clean", "compile", "-T", "1C")
    
    # 添加Java版本profile
    if ($JavaVersionProfile) {
        $mavenArgs += "-P$JavaVersionProfile"
        Write-InfoLog "使用Java版本profile：$JavaVersionProfile"
    }
    
    $mavenArgs += @(
        "-Dmaven.compiler.encoding=UTF-8",
        "-Dproject.build.sourceEncoding=UTF-8"
    )
    
    Write-InfoLog "开始编译整个项目..."
    Write-InfoLog "编译命令：mvn $($mavenArgs -join ' ')"
    
    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList $mavenArgs -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\compile-stdout.log" -RedirectStandardError "$LOG_DIR\compile-stderr.log"
        
        Get-Content "$LOG_DIR\compile-stdout.log", "$LOG_DIR\compile-stderr.log" | Add-Content -Path $MAIN_LOG
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($process.ExitCode -eq 0) {
            Write-SuccessLog "项目编译成功，耗时：$([math]::Round($duration))秒"
            return $true
        } else {
            Write-ErrorLog "项目编译失败，耗时：$([math]::Round($duration))秒"
            Write-ErrorLog "详细错误信息请查看日志文件：$MAIN_LOG"
            return $false
        }
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        Write-ErrorLog "项目编译失败，耗时：$([math]::Round($duration))秒"
        Write-ErrorLog "错误：$($_.Exception.Message)"
        return $false
    }
}

function Invoke-ProjectInstall {
    param([string]$JavaVersionProfile)
    
    Write-StepLog 6 "安装核心SDK模块到本地Maven仓库 (Java版本：$(if ($JavaVersionProfile) { $JavaVersionProfile } else { '默认' }))"
    
    $startTime = Get-Date
    
    # 构建Maven命令参数 - 先安装父POM，再安装核心SDK模块
    $moduleList = "." + "," + ($CORE_SDK_MODULES -join ",")  # 根项目（父POM）+ 核心SDK模块
    $mavenArgs = @("install", "-DskipTests", "-T", "1C", "-pl", $moduleList)
    
    Write-InfoLog "安装父POM和核心SDK模块到.m2仓库：file-transfer-sdk-parent, $($CORE_SDK_MODULES -join ', ')"
    Write-InfoLog "跳过演示模块和独立服务模块，保持本地仓库整洁"
    
    # 添加Java版本profile
    if ($JavaVersionProfile) {
        $mavenArgs += "-P$JavaVersionProfile"
        Write-InfoLog "使用Java版本profile：$JavaVersionProfile"
    }
    
    $mavenArgs += @(
        "-Dmaven.compiler.encoding=UTF-8",
        "-Dproject.build.sourceEncoding=UTF-8"
    )
    
    Write-InfoLog "开始安装核心SDK模块到本地Maven仓库..."
    Write-InfoLog "安装命令：mvn $($mavenArgs -join ' ')"
    
    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList $mavenArgs -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\install-stdout.log" -RedirectStandardError "$LOG_DIR\install-stderr.log"
        
        Get-Content "$LOG_DIR\install-stdout.log", "$LOG_DIR\install-stderr.log" | Add-Content -Path $MAIN_LOG
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($process.ExitCode -eq 0) {
            Write-SuccessLog "核心SDK模块安装成功，耗时：$([math]::Round($duration))秒"
            return $true
        } else {
            Write-ErrorLog "核心SDK模块安装失败，耗时：$([math]::Round($duration))秒"
            Write-ErrorLog "详细错误信息请查看日志文件：$MAIN_LOG"
            return $false
        }
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        Write-ErrorLog "核心SDK模块安装失败，耗时：$([math]::Round($duration))秒"
        Write-ErrorLog "错误：$($_.Exception.Message)"
        return $false
    }
}

function Test-BuildResults {
    Write-StepLog 7 "验证构建结果"
    
    $successCount = 0
    $totalCount = 0
    
    foreach ($module in $PROJECT_MODULES) {
        if (!(Test-Path $module)) {
            continue
        }
        
        $totalCount++
        
        if (Test-Path "$module\target\classes") {
            $classFiles = Get-ChildItem -Path "$module\target\classes" -Filter "*.class" -Recurse
            if ($classFiles.Count -gt 0) {
                Write-InfoLog "模块编译成功：$module (生成 $($classFiles.Count) 个class文件)"
                $successCount++
            } else {
                Write-WarningLog "模块编译异常：$module (未生成class文件)"
            }
        } else {
            Write-WarningLog "模块编译失败：$module (target\classes目录不存在)"
        }
        
        if (Test-Path "$module\target") {
            $jarFiles = Get-ChildItem -Path "$module\target" -Filter "*.jar"
            if ($jarFiles.Count -gt 0) {
                Write-InfoLog "模块JAR文件生成：$module ($($jarFiles.Count) 个JAR文件)"
            }
        }
    }
    
    Write-InfoLog "编译验证结果：$successCount/$totalCount 个模块编译成功"
    
    if ($successCount -eq $totalCount) {
        Write-SuccessLog "所有模块编译验证通过"
        return $true
    } else {
        Write-WarningLog "部分模块编译验证失败"
        return $true  # 允许部分失败
    }
}

# ==================== 测试功能函数 ====================

function Invoke-UnitTests {
    param([string]$JavaVersionProfile)
    
    Write-StepLog 8 "运行单元测试 (Java版本：$(if ($JavaVersionProfile) { $JavaVersionProfile } else { '默认' }))"
    Write-TestPhaseLog "开始单元测试阶段"
    
    $totalModules = 0
    $successModules = 0
    $startTime = Get-Date
    
    foreach ($module in $TEST_MODULES) {
        if (!(Test-Path $module)) {
            Write-WarningLog "模块目录不存在，跳过：$module"
            continue
        }
        
        $totalModules++
        
        Write-TestPhaseLog "运行模块单元测试：$module"
        
        # 构建Maven测试命令参数
        $mavenArgs = @("test", "-pl", $module)
        
        # 添加Java版本profile
        if ($JavaVersionProfile) {
            $mavenArgs += "-P$JavaVersionProfile"
            Write-InfoLog "单元测试使用Java版本profile：$JavaVersionProfile"
        }
        
        # Maven配置中已经有excludes规则，不需要额外过滤
        $mavenArgs += "-Dsurefire.failIfNoSpecifiedTests=false"
        
        try {
            $process = Start-Process -FilePath "mvn" -ArgumentList $mavenArgs -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\test-$module-stdout.log" -RedirectStandardError "$LOG_DIR\test-$module-stderr.log"
            
            Get-Content "$LOG_DIR\test-$module-stdout.log", "$LOG_DIR\test-$module-stderr.log" | Add-Content -Path $MAIN_LOG
            
            if ($process.ExitCode -eq 0) {
                Write-SuccessLog "模块单元测试通过：$module"
                $successModules++
            } else {
                Write-ErrorLog "模块单元测试失败：$module"
            }
        } catch {
            Write-ErrorLog "模块单元测试失败：$module - $($_.Exception.Message)"
        }
    }
    
    foreach ($demoModule in $DEMO_MODULES) {
        if (Test-Path $demoModule) {
            Write-InfoLog "跳过演示模块单元测试：$demoModule（演示模块将在集成测试阶段执行）"
        }
    }
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    Write-TestPhaseLog "单元测试阶段完成"
    Write-InfoLog "单元测试结果：$successModules/$totalModules 个模块通过，耗时：$([math]::Round($duration))秒"
    
    if ($successModules -eq $totalModules) {
        Write-SuccessLog "所有单元测试通过"
        return $true
    } else {
        Write-ErrorLog "部分单元测试失败"
        return $false
    }
}

# ==================== 服务器管理函数 ====================

function Start-TestServer {
    param([string]$JavaVersionProfile)
    
    Write-TestPhaseLog "启动测试服务器"
    Write-InfoLog "启动测试服务器（端口：$TEST_SERVER_PORT）..."
    
    if (!(Test-Path $STANDALONE_MODULE)) {
        Write-ErrorLog "独立服务器模块不存在，无法启动测试服务器：$STANDALONE_MODULE"
        return $false
    }
    
    if (!(Test-Path "$STANDALONE_MODULE\start-server.ps1")) {
        Write-ErrorLog "独立服务器启动脚本不存在，无法启动测试服务器"
        return $false
    }
    
    # 先尝试停止可能运行的测试服务器
    Write-InfoLog "清理可能运行的测试服务器..."
    Stop-AllTestServers
    
    # 等待端口释放
    Start-Sleep -Seconds 2
    
    # 检查端口占用情况并强制清理
    Write-InfoLog "检查端口 $TEST_SERVER_PORT 的占用情况..."
    try {
        $processes = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    ForEach-Object { Get-Process -Id $_ -ErrorAction SilentlyContinue }
        
        if ($processes) {
            Write-WarningLog "发现占用端口 $TEST_SERVER_PORT 的进程：$($processes.Id -join ', ')"
            Write-InfoLog "强制停止这些进程..."
            $processes | Stop-Process -Force
            Start-Sleep -Seconds 3
            
            # 再次检查
            $remainingProcesses = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue
            if ($remainingProcesses) {
                Write-ErrorLog "无法释放端口 $TEST_SERVER_PORT，仍有进程占用"
                return $false
            } else {
                Write-InfoLog "端口 $TEST_SERVER_PORT 已成功释放"
            }
        } else {
            Write-InfoLog "端口 $TEST_SERVER_PORT 当前未被占用"
        }
    } catch {
        Write-InfoLog "端口检查完成（可能未被占用）"
    }
    
    Write-InfoLog "确保独立服务器模块已编译..."
    
    # 根据Java版本profile动态构建jar文件名 - 现在所有版本都有后缀
    $jarSuffix = "-$JavaVersionProfile"

    $standaloneJar = "$STANDALONE_MODULE\target\file-transfer-server-standalone-1.0.0$jarSuffix.jar"
    
    if (!(Test-Path $standaloneJar)) {
        Write-InfoLog "编译独立服务器模块..."
        try {
            # 构建编译命令参数，使用与当前构建相同的profile
            $compileArgs = @("package", "-pl", $STANDALONE_MODULE, "-DskipTests", "-q")
            if ($JavaVersionProfile) {
                $compileArgs += "-P$JavaVersionProfile"
            }
            
            $process = Start-Process -FilePath "mvn" -ArgumentList $compileArgs -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\standalone-compile-stdout.log" -RedirectStandardError "$LOG_DIR\standalone-compile-stderr.log"
            
            Get-Content "$LOG_DIR\standalone-compile-stdout.log", "$LOG_DIR\standalone-compile-stderr.log" | Add-Content -Path $MAIN_LOG
            
            if ($process.ExitCode -ne 0) {
                Write-ErrorLog "独立服务器模块编译失败"
                return $false
            }
            
            # 验证jar文件是否生成
            if (!(Test-Path $standaloneJar)) {
                Write-ErrorLog "独立服务器jar文件未生成：$standaloneJar"
                return $false
            }
        } catch {
            Write-ErrorLog "独立服务器模块编译失败：$($_.Exception.Message)"
            return $false
        }
    }
    
    Write-InfoLog "独立服务器jar文件：$standaloneJar"
    
    Write-InfoLog "使用独立服务器启动脚本启动测试服务器..."
    Push-Location $STANDALONE_MODULE
    
    try {
        # 构建启动命令，正确处理包含空格的路径
        $scriptCommand = "& '.\start-server.ps1' start -Port $TEST_SERVER_PORT -Background"
        
        # 如果设置了自定义JavaHome，添加到命令中
        if ($env:JAVA_HOME) {
            $scriptCommand += " -JavaHome '$env:JAVA_HOME'"
        }
        
        Write-InfoLog "启动命令：$scriptCommand"
        
        # 使用异步作业方式启动，避免进程卡住
        Write-InfoLog "启动服务器启动作业..."
        $job = Start-Job -ScriptBlock {
            param($Command, $WorkingDir, $LogFile)
            try {
                Set-Location $WorkingDir

                # 设置作业环境变量，让启动脚本知道它在作业中运行
                $env:PS_JOB_MODE = "true"

                # 执行命令并捕获输出
                $output = Invoke-Expression $Command 2>&1

                # 将输出写入日志文件
                if ($output) {
                    Add-Content -Path $LogFile -Value $output -ErrorAction SilentlyContinue
                }

                return @{
                    Success = $true
                    Output = $output
                    Error = $null
                }
            } catch {
                $errorMsg = $_.Exception.Message
                # 将错误也写入日志文件
                Add-Content -Path $LogFile -Value "ERROR: $errorMsg" -ErrorAction SilentlyContinue

                return @{
                    Success = $false
                    Output = $null
                    Error = $errorMsg
                }
            }
        } -ArgumentList $scriptCommand, (Get-Location).Path, $MAIN_LOG
        
        Write-InfoLog "等待启动作业完成（最多90秒）..."
        $completed = Wait-Job $job -Timeout 90
        
        if ($completed) {
            $jobResult = Receive-Job $job
            if ($jobResult.Success) {
                Write-InfoLog "服务器启动脚本执行成功"
                if ($jobResult.Output) {
                    $jobResult.Output | Add-Content -Path $MAIN_LOG
                }
                $exitCode = 0
            } else {
                Write-ErrorLog "服务器启动脚本执行失败：$($jobResult.Error)"
                $exitCode = 1
            }
        } else {
            Write-WarningLog "服务器启动脚本执行超时（90秒），停止作业"
            Stop-Job $job
            $exitCode = 1
        }
        
        Remove-Job $job -Force
        $processResult = @{ ExitCode = $exitCode }
        
        # 无论脚本退出码如何，都尝试验证服务器是否真正启动
        Pop-Location
        Write-InfoLog "验证测试服务器是否成功启动..."
        
        # 等待服务器完全启动
        Start-Sleep -Seconds 3
        
        # 尝试连接服务器健康检查端点
        $serverStarted = $false
        $waitCount = 0
        $maxWaitTime = 45  # 最多等待45秒
        
        while ($waitCount -lt $maxWaitTime) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$TEST_SERVER_PORT/filetransfer/actuator/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $serverStarted = $true
                    break
                }
            } catch {
                # 忽略连接错误，继续等待
            }
            
            if ($waitCount % 10 -eq 0 -and $waitCount -gt 0) {
                Write-InfoLog "等待服务器响应中... ($waitCount/$maxWaitTime 秒)"
            }
            
            Start-Sleep -Seconds 1
            $waitCount++
        }
        
        if ($serverStarted) {
            Write-SuccessLog "测试服务器启动成功并响应健康检查"
            Write-InfoLog "服务器地址: http://localhost:$TEST_SERVER_PORT"
            Write-InfoLog "API文档: http://localhost:$TEST_SERVER_PORT/filetransfer/doc.html"
            Write-InfoLog "健康检查: http://localhost:$TEST_SERVER_PORT/filetransfer/actuator/health"
            
            # 尝试获取并保存PID
            Push-Location $STANDALONE_MODULE
            if (Test-Path "logs\server.pid") {
                $serverPid = Get-Content "logs\server.pid"
                Set-Content -Path "..\$SERVER_PID_FILE" -Value $serverPid
                Write-InfoLog "测试服务器PID：$serverPid"
            }
            Pop-Location
            
            return $true
        } else {
            Write-ErrorLog "测试服务器启动失败或无法响应健康检查（等待 $maxWaitTime 秒）"
            Write-InfoLog "尝试清理可能的残留进程..."
            
            # 尝试停止服务器
            Push-Location $STANDALONE_MODULE
            try {
                & ".\start-server.ps1" stop 2>&1 | Add-Content -Path $MAIN_LOG
            } catch {
                Write-WarningLog "停止服务器失败：$($_.Exception.Message)"
            }
            Pop-Location
            
            return $false
        }
    } catch {
        Pop-Location
        Write-ErrorLog "启动测试服务器时发生错误：$($_.Exception.Message)"
        return $false
    }
}

function Stop-TestServer {
    Write-TestPhaseLog "停止测试服务器"
    Write-InfoLog "停止测试服务器..."
    
    if ((Test-Path $STANDALONE_MODULE) -and (Test-Path "$STANDALONE_MODULE\start-server.ps1")) {
        Write-InfoLog "使用独立服务器启动脚本停止测试服务器..."
        Push-Location $STANDALONE_MODULE
        try {
            & ".\start-server.ps1" stop 2>&1 | Add-Content -Path $MAIN_LOG
        } catch {
            Write-WarningLog "使用启动脚本停止服务器失败：$($_.Exception.Message)"
        }
        Pop-Location
    }
    
    if (Test-Path $SERVER_PID_FILE) {
        try {
            $serverPid = Get-Content $SERVER_PID_FILE
            $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
            if ($process) {
                Write-InfoLog "通过PID停止测试服务器（PID：$serverPid）..."
                Stop-Process -Id $serverPid -Force
                
                $waitCount = 0
                while ($waitCount -lt $SERVER_SHUTDOWN_TIMEOUT_SECONDS) {
                    $proc = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
                    if (!$proc) {
                        break
                    }
                    Start-Sleep -Seconds 1
                    $waitCount++
                }
                
                if (Get-Process -Id $serverPid -ErrorAction SilentlyContinue) {
                    Write-WarningLog "强制停止测试服务器进程（PID：$serverPid）"
                    Stop-Process -Id $serverPid -Force
                }
                
                Write-InfoLog "测试服务器已停止（PID：$serverPid）"
            }
            Remove-Item $SERVER_PID_FILE -Force
        } catch {
            Write-WarningLog "停止测试服务器时发生错误：$($_.Exception.Message)"
        }
    }
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    ForEach-Object { Get-Process -Id $_ -ErrorAction SilentlyContinue }
        
        if ($processes) {
            Write-WarningLog "强制停止占用端口 $TEST_SERVER_PORT 的进程：$($processes.Id -join ', ')"
            $processes | Stop-Process -Force
            Start-Sleep -Seconds 2
        }
    } catch {
        # 忽略错误
    }
    
    Write-SuccessLog "测试服务器停止完成"
}

function Invoke-ClientDemoTest {
    Write-TestPhaseLog "运行客户端演示测试"
    
    if (!(Test-Path "file-transfer-client-demo")) {
        Write-WarningLog "客户端演示模块不存在，跳过演示测试"
        return $false
    }
    
    $demoStartTime = Get-Date
    $demoSuccess = $true
    
    $demoServerHost = "localhost"
    $demoServerPort = $TEST_SERVER_PORT
    
    Write-InfoLog "启动客户端演示测试（服务器：${demoServerHost}:${demoServerPort}）"
    
    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "exec:java", "-pl", "file-transfer-client-demo",
            "-Dexec.mainClass=com.sdesrd.filetransfer.demo.FileTransferClientDemo",
            "-Ddemo.server.host=$demoServerHost",
            "-Ddemo.server.port=$demoServerPort",
            "-Ddemo.user.name=demo",
            "-Ddemo.user.secret=demo-secret-key-2024",
            "-Ddemo.upload.dir=demo-files/upload",
            "-Ddemo.download.dir=demo-files/download",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\demo-stdout.log" -RedirectStandardError "$LOG_DIR\demo-stderr.log"
        
        Get-Content "$LOG_DIR\demo-stdout.log", "$LOG_DIR\demo-stderr.log" | Add-Content -Path $MAIN_LOG
        
        if ($process.ExitCode -eq 0) {
            Write-SuccessLog "客户端演示测试执行成功"
        } else {
            Write-WarningLog "客户端演示测试执行失败或超时（${DEMO_TEST_TIMEOUT_SECONDS}秒）"
            $demoSuccess = $false
        }
    } catch {
        Write-WarningLog "客户端演示测试执行失败：$($_.Exception.Message)"
        $demoSuccess = $false
    }
    
    $demoEndTime = Get-Date
    $demoDuration = ($demoEndTime - $demoStartTime).TotalSeconds
    
    Write-InfoLog "客户端演示测试耗时：$([math]::Round($demoDuration))秒"
    
    if (Test-Path "demo-files") {
        Remove-Item "demo-files" -Recurse -Force
        Write-InfoLog "清理演示文件目录"
    }
    
    return $demoSuccess
}

function Invoke-IntegrationTests {
    param([string]$JavaVersionProfile)
    
    Write-StepLog 9 "运行集成测试"
    Write-TestPhaseLog "开始集成测试阶段"
    
    $startTime = Get-Date
    $integrationSuccess = $true
    
    if (!(Start-TestServer $JavaVersionProfile)) {
        Write-ErrorLog "无法启动测试服务器，跳过集成测试"
        return $false
    }
    
    Write-InfoLog "等待测试服务器稳定..."
    Start-Sleep -Seconds 3
    
    Write-TestPhaseLog "检查并运行端到端测试"
    $endToEndTestFound = $false
    foreach ($module in $PROJECT_MODULES) {
        if (Test-Path "$module\src\test\java") {
            $testFiles = Get-ChildItem -Path "$module\src\test\java" -Recurse -Include "*EndToEndTest.java", "*EndToEndTransferTest.java", "*IntegrationTest.java"
            if ($testFiles.Count -gt 0) {
                Write-TestPhaseLog "在模块 $module 中发现端到端/集成测试，正在执行..."
                
                # 构建集成测试Maven命令参数
                $mavenArgs = @("test", "-pl", $module)
                
                # 添加Java版本profile
                if ($JavaVersionProfile) {
                    $mavenArgs += "-P$JavaVersionProfile"
                    Write-InfoLog "集成测试使用Java版本profile：$JavaVersionProfile"
                }
                
                $mavenArgs += @(
                    "-Dtest=*EndToEndTest,*EndToEndTransferTest,*IntegrationTest",
                    "-Dserver.port=$TEST_SERVER_PORT",
                    "-Dtest.server.host=localhost",
                    "-Dtest.server.port=$TEST_SERVER_PORT",
                    "-Dtest.user.name=demo",
                    "-Dtest.user.secret=demo-secret-key-2024",
                    "-Dmaven.compiler.source=1.8",
                    "-Dmaven.compiler.target=1.8"
                )
                
                try {
                    $process = Start-Process -FilePath "mvn" -ArgumentList $mavenArgs -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\integration-$module-stdout.log" -RedirectStandardError "$LOG_DIR\integration-$module-stderr.log"
                    
                    Get-Content "$LOG_DIR\integration-$module-stdout.log", "$LOG_DIR\integration-$module-stderr.log" | Add-Content -Path $MAIN_LOG
                    
                    if ($process.ExitCode -eq 0) {
                        Write-SuccessLog "模块 $module 端到端/集成测试通过"
                        $endToEndTestFound = $true
                    } else {
                        Write-ErrorLog "模块 $module 端到端/集成测试失败"
                        $integrationSuccess = $false
                    }
                } catch {
                    Write-ErrorLog "模块 $module 端到端/集成测试失败：$($_.Exception.Message)"
                    $integrationSuccess = $false
                }
            }
        }
    }
    
    if (Invoke-ClientDemoTest) {
        Write-SuccessLog "客户端演示测试通过"
    } else {
        Write-WarningLog "客户端演示测试失败，但不影响整体测试结果"
    }
    
    Stop-TestServer
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    Write-TestPhaseLog "集成测试阶段完成"
    Write-InfoLog "集成测试耗时：$([math]::Round($duration))秒"
    
    if ($integrationSuccess) {
        Write-SuccessLog "集成测试通过"
        return $true
    } else {
        Write-ErrorLog "集成测试失败"
        return $false
    }
}

# ==================== 报告生成函数 ====================

function Get-TestResults {
    $totalTests = 0
    $failedTests = 0
    $skippedTests = 0
    
    # 收集所有模块的测试结果（包括单元测试和集成测试）
    $allModules = $PROJECT_MODULES + $DEMO_MODULES
    # 添加独立服务模块
    if (Test-Path $STANDALONE_MODULE) {
        $allModules += $STANDALONE_MODULE
    }
    
    Write-InfoLog "开始收集测试结果..."
    
    foreach ($module in $allModules) {
        if (!(Test-Path "$module\target\surefire-reports")) {
            Write-InfoLog "$module`: 跳过（未找到测试报告目录）"
            continue
        }
        
        # 统计测试文件数量
        $moduleTestFiles = Get-ChildItem -Path "$module\target\surefire-reports" -Filter "TEST-*.xml"
        
        if ($moduleTestFiles.Count -gt 0) {
            # 解析测试结果XML文件
            $moduleTests = 0
            $moduleFailures = 0
            $moduleErrors = 0
            $moduleSkipped = 0
            
            foreach ($xmlFile in $moduleTestFiles) {
                if (Test-Path $xmlFile.FullName) {
                    try {
                        [xml]$xmlContent = Get-Content $xmlFile.FullName
                        $testsuite = $xmlContent.testsuite
                        
                        $tests = if ($testsuite.tests) { [int]$testsuite.tests } else { 0 }
                        $failures = if ($testsuite.failures) { [int]$testsuite.failures } else { 0 }
                        $errors = if ($testsuite.errors) { [int]$testsuite.errors } else { 0 }
                        $skipped = if ($testsuite.skipped) { [int]$testsuite.skipped } else { 0 }
                        
                        $moduleTests += $tests
                        $moduleFailures += $failures
                        $moduleErrors += $errors
                        $moduleSkipped += $skipped
                    } catch {
                        Write-WarningLog "无法解析测试结果文件：$($xmlFile.FullName)"
                    }
                }
            }
            
            $moduleFailed = $moduleFailures + $moduleErrors
            
            $totalTests += $moduleTests
            $failedTests += $moduleFailed
            $skippedTests += $moduleSkipped
            
            if ($moduleTests -gt 0) {
                Write-InfoLog "$module`: $moduleTests 个测试，$moduleFailed 个失败，$moduleSkipped 个跳过"
            }
        } else {
            Write-InfoLog "$module`: 跳过（未找到测试结果文件）"
        }
    }
    
    $passedTests = $totalTests - $failedTests - $skippedTests
    
    Write-Host ""
    Write-Host "==========================================" -ForegroundColor White
    Write-Host "           测试结果汇总" -ForegroundColor White
    Write-Host "==========================================" -ForegroundColor White
    Write-Host "总测试数: $totalTests"
    Write-Host "通过测试: $passedTests"
    Write-Host "失败测试: $failedTests"
    Write-Host "跳过测试: $skippedTests"
    
    if ($failedTests -eq 0) {
        Write-Host "测试结果: " -NoNewline
        Write-Host "全部通过" -ForegroundColor Green
        return $true
    } else {
        Write-Host "测试结果: " -NoNewline
        Write-Host "有失败" -ForegroundColor Red
        return $false
    }
}

function New-FinalReport {
    param([string]$ExecutionMode, [string]$JavaVersionProfile)
    
    Write-StepLog 10 "生成最终报告"
    
    $reportFile = "$LOG_DIR\final-report-$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    
    $reportContent = @"
========================================================
        文件传输SDK多版本构建和测试最终报告
========================================================
执行时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
脚本版本：$SCRIPT_VERSION
Java版本：$(try { & java -version 2>&1 | Select-Object -First 1 } catch { "未知" })
Maven版本：$(try { & mvn -version | Select-Object -First 1 } catch { "未知" })
目标Java版本：$(if ($JavaVersionProfile) { $JavaVersionProfile } else { 'java8 (默认)' })

执行模式：
"@

    if ($ExecutionMode -eq $MODE_BUILD) {
        $reportContent += "  构建模式：编译并安装父POM和核心SDK模块`n"
    } else {
        $reportContent += "  完整模式：构建 + 测试 + 安装父POM和核心SDK模块`n"
    }
    
    $reportContent += "`n构建配置：`n"
    $reportContent += "  Maven Profile：$(if ($JavaVersionProfile) { $JavaVersionProfile } else { 'java8 (默认)' })`n"
    $reportContent += "  JAR后缀：$(if ($JavaVersionProfile) { "-$JavaVersionProfile" } else { '' })`n"
    
    $reportContent += "`n项目模块：`n"
    foreach ($module in $PROJECT_MODULES) {
        if (Test-Path $module) {
            $reportContent += "  ✓ $module`n"
        } else {
            $reportContent += "  ✗ $module (目录不存在)`n"
        }
    }
    
    $reportContent += "`n构建结果：`n"
    foreach ($module in $PROJECT_MODULES) {
        if (!(Test-Path $module)) {
            continue
        }
        
        if (Test-Path "$module\target\classes") {
            $classCount = (Get-ChildItem -Path "$module\target\classes" -Filter "*.class" -Recurse).Count
            $reportContent += "  $module`: 编译成功 ($classCount 个class文件)`n"
        } else {
            $reportContent += "  $module`: 编译失败`n"
        }
    }
    
    if ($ExecutionMode -eq $MODE_BUILD_TEST) {
        $reportContent += "`n测试结果详情：`n"
        
        foreach ($module in $TEST_MODULES) {
            if (!(Test-Path "$module\target\surefire-reports")) {
                $reportContent += "  $module`: 未运行测试`n"
                continue
            }
            
            $testFiles = (Get-ChildItem -Path "$module\target\surefire-reports" -Filter "TEST-*.xml").Count
            if ($testFiles -gt 0) {
                $reportContent += "  $module`: $testFiles 个测试套件`n"
            } else {
                $reportContent += "  $module`: 无测试结果`n"
            }
        }
        
        foreach ($demoModule in $DEMO_MODULES) {
            if (Test-Path $demoModule) {
                $reportContent += "  $demoModule`: 演示模块（通过集成测试中的演示程序执行验证）`n"
            }
        }
    }
    
    $reportContent += "`n详细日志：$MAIN_LOG`n"
    $reportContent += "========================================================"
    
    Set-Content -Path $reportFile -Value $reportContent -Encoding UTF8
    
    Write-InfoLog "最终报告已生成：$reportFile"
    
    # 显示报告内容
    Get-Content $reportFile | ForEach-Object { Write-Host $_ }
    
    return $true
}

# ==================== 帮助信息 ====================

function Show-Help {
    Write-Host "========================================================" -ForegroundColor White
    Write-Host "    $SCRIPT_NAME" -ForegroundColor White
    Write-Host "    版本：$SCRIPT_VERSION" -ForegroundColor White
    Write-Host "========================================================" -ForegroundColor White
    Write-Host ""
    Write-Host "用法: .\build-and-test.ps1 [模式] [选项]"
    Write-Host ""
    Write-Host "执行模式："
    Write-Host "  -Mode build              仅执行构建（编译+安装），不运行测试"
    Write-Host "  -Mode build-test         执行完整流程（构建+测试）[默认]"
    Write-Host ""
    Write-Host "Java环境选项："
    Write-Host "  -JavaHome PATH           指定Java JDK路径（可选，默认使用系统Java）"
    Write-Host "  -JavaVersion VER         指定Java目标版本 (java8|java11|java17|java21)"
    Write-Host ""
    Write-Host "其他选项："
    Write-Host "  -Help                    显示此帮助信息"
    Write-Host ""
    Write-Host "使用示例："
    Write-Host "  .\build-and-test.ps1                                      # 完整构建和测试流程 (默认Java 8)"
    Write-Host "  .\build-and-test.ps1 -Mode build                          # 仅构建项目"
    Write-Host "  .\build-and-test.ps1 -Mode build-test                     # 构建并测试项目"
    Write-Host "  .\build-and-test.ps1 -JavaHome 'C:\Java\jdk21'            # 使用指定的Java路径"
    Write-Host "  .\build-and-test.ps1 -Mode build -JavaVersion java21      # 仅构建Java 21版本"
    Write-Host "  .\build-and-test.ps1 -Mode build-test -JavaVersion java17 # 构建并测试Java 17版本"
    Write-Host ""
    Write-Host "支持的Java版本："
    foreach ($version in $SUPPORTED_JAVA_VERSIONS) {
        Write-Host "  - $version"
    }
    Write-Host ""
    Write-Host "默认配置："
    Write-Host "  Java路径: 使用系统JAVA_HOME环境变量（可通过-JavaHome参数指定）"
    Write-Host "  Java版本: java8 (使用Java 8语言特性，可编译为不同字节码版本)"
    Write-Host "  测试端口: $TEST_SERVER_PORT"
    Write-Host "  日志目录: $LOG_DIR"
    Write-Host ""
    Write-Host "说明："
    Write-Host "  - build模式：编译项目并安装核心SDK模块到本地Maven仓库"
    Write-Host "  - build-test模式：在build基础上运行单元测试和集成测试"
    Write-Host "  - 默认安装父POM和核心SDK模块（file-transfer-sdk-parent, file-transfer-server-sdk, file-transfer-client-sdk）到.m2仓库"
    Write-Host "  - 跳过安装演示模块和独立服务模块，保持本地仓库整洁"
    Write-Host "  - 集成测试会自动启动和停止file-transfer-standalone服务"
    Write-Host "  - 多版本构建：使用Java 8语法，但可编译为不同Java版本的字节码"
    Write-Host "  - JAR文件会包含版本后缀，如: xxx-java11.jar, xxx-java21.jar"
    Write-Host "  - 脚本会优先使用JAVA_HOME环境变量，如果无效则使用系统默认Java"
    Write-Host "  - 所有日志都会记录到日志文件中，便于问题排查"
    Write-Host ""
}

# ==================== 主程序 ====================

function Invoke-Cleanup {
    param([int]$ExitCode)
    
    # 停止测试服务器
    Stop-TestServer
    
    if ($ExitCode -ne 0) {
        Write-ErrorLog "执行过程中发生错误，退出码：$ExitCode"
        Write-InfoLog "详细错误信息请查看日志文件：$MAIN_LOG"
    }
}

function Main {
    # 显示帮助信息
    if ($Help) {
        Show-Help
        exit 0
    }
    
    # 验证执行模式
    if ($Mode -notin @($MODE_BUILD, $MODE_BUILD_TEST)) {
        Write-ErrorLog "未知模式: $Mode"
        Write-Host ""
        Show-Help
        exit 1
    }
    
    # 验证Java版本
    if (!(Test-JavaVersion $JavaVersion)) {
        Show-Help
        exit 1
    }
    
    # 设置默认Java版本profile（如果未指定）
    if (!$JavaVersion) {
        $JavaVersion = "java8"
        Write-InfoLog "未指定Java版本，使用默认值：$JavaVersion"
    }
    
    # 显示脚本头部信息
    Show-Header
    
    # 初始化日志
    Initialize-Logging
    
    Write-InfoLog "执行模式：$Mode"
    Write-InfoLog "Java目标版本：$JavaVersion"
    
    # 执行主要流程
    $executionFailed = $false
    
    try {
        # 步骤1-4：环境检查和准备
        if (!(Setup-JavaEnvironment $JavaHome)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Test-MavenEnvironment)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Test-ProjectStructure)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Clear-Environment)) {
            $executionFailed = $true
        # 步骤5-7：构建流程
        } elseif (!$executionFailed -and !(Invoke-ProjectCompile $JavaVersion)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Invoke-ProjectInstall $JavaVersion)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Test-BuildResults)) {
            $executionFailed = $true
        }
        
        # 步骤8-11：测试流程（仅在build-test模式下执行）
        if (!$executionFailed -and ($Mode -eq $MODE_BUILD_TEST)) {
            Write-TestPhaseLog "开始测试流程"
            
            if (!(Invoke-UnitTests $JavaVersion)) {
                $executionFailed = $true
            } elseif (!(Invoke-IntegrationTests $JavaVersion)) {
                $executionFailed = $true
            }
            
            # 收集测试结果
            if (!(Get-TestResults)) {
                $executionFailed = $true
            }
        }
        
        # 生成最终报告
        New-FinalReport $Mode $JavaVersion | Out-Null
        
    } catch {
        Write-ErrorLog "执行过程中发生未捕获的错误：$($_.Exception.Message)"
        $executionFailed = $true
    } finally {
        # 清理资源
        Invoke-Cleanup $(if ($executionFailed) { 1 } else { 0 })
    }
    
    # 返回结果
    if ($executionFailed) {
        Write-ErrorLog "执行失败"
        exit 1
    } else {
        if ($Mode -eq $MODE_BUILD) {
            Write-SuccessLog "构建成功完成"
        } else {
            Write-SuccessLog "构建和测试成功完成"
        }
        exit 0
    }
}

# 执行主函数
Main 