package com.sdesrd.filetransfer.server.dto;

import lombok.Data;

/**
 * 文件上传初始化响应
 * 重构后的版本：使用ULID作为fileId，返回最终存储的文件名
 * 支持秒传时返回完整的文件信息
 */
@Data
public class FileUploadInitResponse {

    /**
     * 传输ID（UUID格式）
     */
    private String transferId;

    /**
     * 文件ID（ULID格式）
     */
    private String fileId;

    /**
     * 最终存储的文件名（格式：md5.{后缀名} 或 md5）
     */
    private String fileName;

    /**
     * 是否秒传
     */
    private Boolean fastUpload;

    /**
     * 分块大小
     */
    private Long chunkSize;

    /**
     * 总分块数
     */
    private Integer totalChunks;

    /**
     * 文件相对路径（相对于存储根目录）
     * 格式：YYYYMM/fileId/fileName
     * 秒传时有值，普通上传时为null
     */
    private String relativePath;

    /**
     * 文件MD5值
     * 秒传时有值，普通上传时为null
     */
    private String fileMd5;

    /**
     * 上传完成时间（ISO格式）
     * 秒传时有值，普通上传时为null
     */
    private String completeTime;

    /**
     * 文件大小（字节）
     * 秒传时有值，普通上传时为null
     */
    private Long fileSize;

    /**
     * 传输耗时（毫秒）
     * 秒传时有值，普通上传时为null
     */
    private Long transferDuration;
}