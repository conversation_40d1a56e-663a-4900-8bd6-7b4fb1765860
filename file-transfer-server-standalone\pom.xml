<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.0</version>
        <relativePath/>
    </parent>

    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-server-standalone</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <name>文件传输独立服务端</name>
    <description>文件传输服务端独立应用，可作为独立进程运行</description>

    <properties>
        <java.version>8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- 多版本构建配置 -->
        <java.target.version>8</java.target.version>
        <java.release.version>8</java.release.version>
        <jar.classifier.suffix>-java8</jar.classifier.suffix>
        <jar.classifier>java8</jar.classifier>
        
        <!-- 独立部署相关配置 -->
        <standalone.mainClass>com.sdesrd.filetransfer.server.standalone.FileTransferServerApplication</standalone.mainClass>
        <standalone.finalName>file-transfer-server-standalone-${project.version}${jar.classifier.suffix}</standalone.finalName>
    </properties>

    <!-- 多版本构建profiles -->
    <profiles>
        <!-- Java 8构建 -->
        <profile>
            <id>java8</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <java.target.version>8</java.target.version>
                <java.release.version>8</java.release.version>
                <jar.classifier.suffix>-java8</jar.classifier.suffix>
                <jar.classifier>java8</jar.classifier>
            </properties>
        </profile>
        
        <!-- Java 11构建 -->
        <profile>
            <id>java11</id>
            <properties>
                <java.target.version>11</java.target.version>
                <java.release.version>11</java.release.version>
                <jar.classifier.suffix>-java11</jar.classifier.suffix>
                <jar.classifier>java11</jar.classifier>
            </properties>
        </profile>
        
        <!-- Java 17构建 -->
        <profile>
            <id>java17</id>
            <properties>
                <java.target.version>17</java.target.version>
                <java.release.version>17</java.release.version>
                <jar.classifier.suffix>-java17</jar.classifier.suffix>
                <jar.classifier>java17</jar.classifier>
            </properties>
        </profile>
        
        <!-- Java 21构建 -->
        <profile>
            <id>java21</id>
            <properties>
                <java.target.version>21</java.target.version>
                <java.release.version>21</java.release.version>
                <jar.classifier.suffix>-java21</jar.classifier.suffix>
                <jar.classifier>java21</jar.classifier>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <!-- 文件传输服务端SDK -->
        <dependency>
            <groupId>com.sdesrd.filetransfer</groupId>
            <artifactId>file-transfer-server-sdk</artifactId>
            <version>1.0.0</version>
            <classifier>${jar.classifier}</classifier>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- 配置处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 监控端点 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- 日志 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${standalone.finalName}</finalName>
        
        <plugins>
            <!-- ====================================== -->
            <!-- 1. Maven JAR Plugin - 生成主应用JAR包 -->
            <!-- ====================================== -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <!-- 启用依赖包添加到 classpath -->
                            <addClasspath>true</addClasspath>
                            <!-- 指定依赖包所在目录前缀 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- 指定主类 -->
                            <mainClass>${standalone.mainClass}</mainClass>
                            <!-- 添加版本信息 -->
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                        <manifestEntries>
                            <!-- 自定义 manifest 条目 -->
                            <Implementation-Title>${project.name}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Vendor>SDESRD</Implementation-Vendor>
                            <Built-By>${user.name}</Built-By>
                            <Build-Jdk>${java.version}</Build-Jdk>
                            <Build-Time>${maven.build.timestamp}</Build-Time>
                        </manifestEntries>
                    </archive>
                    <!-- 排除依赖包，只保留应用代码 -->
                    <excludes>
                        <exclude>**/application*.yml</exclude>
                        <exclude>**/application*.yaml</exclude>
                        <exclude>**/application*.properties</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- ========================================== -->
            <!-- 2. Maven Dependency Plugin - 复制依赖JAR包 -->
            <!-- ========================================== -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <!-- 复制所有依赖到 lib 目录 -->
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 输出目录 -->
                            <outputDirectory>${project.build.directory}/${standalone.finalName}/lib</outputDirectory>
                            <!-- 不复制 provided 和 test 范围的依赖 -->
                            <includeScope>runtime</includeScope>
                            <!-- 覆盖已存在的文件 -->
                            <overWriteReleases>true</overWriteReleases>
                            <overWriteSnapshots>true</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <!-- 去除版本号 -->
                            <stripVersion>false</stripVersion>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- ========================================== -->
            <!-- 3. Maven Resources Plugin - 处理资源文件  -->
            <!-- ========================================== -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <!-- 复制配置文件 -->
                    <execution>
                        <id>copy-config-files</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/${standalone.finalName}/config</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>**/*.yml</include>
                                        <include>**/*.yaml</include>
                                        <include>**/*.properties</include>
                                        <include>**/*.xml</include>
                                    </includes>
                                    <!-- 排除不需要的配置文件 -->
                                    <excludes>
                                        <exclude>**/logback-spring.xml</exclude>
                                        <exclude>**/application-dev.*</exclude>
                                        <exclude>**/application-test.*</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                            <!-- 启用变量替换 -->
                            <filtering>true</filtering>
                        </configuration>
                    </execution>
                    
                    <!-- 复制启动脚本 -->
                    <execution>
                        <id>copy-startup-scripts</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/${standalone.finalName}/bin</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>.</directory>
                                    <includes>
                                        <include>start-server.sh</include>
                                        <include>start-server.ps1</include>
                                    </includes>
                                    <!-- 启用变量替换 -->
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    
                    <!-- 复制文档和许可证文件 -->
                    <execution>
                        <id>copy-documentation</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/${standalone.finalName}/docs</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>..</directory>
                                    <includes>
                                        <include>README.md</include>
                                        <include>LICENSE</include>
                                        <include>integration-guide.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- ============================================ -->
            <!-- 4. Maven Antrun Plugin - 复制主JAR到分发目录 -->
            <!-- ============================================ -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>copy-main-jar</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 复制主应用JAR包到分发目录 -->
                                <copy file="${project.build.directory}/${standalone.finalName}.jar" 
                                      tofile="${project.build.directory}/${standalone.finalName}/${standalone.finalName}.jar"/>
                                
                                <!-- 创建必要的运行时目录 -->
                                <mkdir dir="${project.build.directory}/${standalone.finalName}/data"/>
                                <mkdir dir="${project.build.directory}/${standalone.finalName}/logs"/>
                                <mkdir dir="${project.build.directory}/${standalone.finalName}/temp"/>
                                
                                <!-- 设置启动脚本的执行权限（Linux/Mac） -->
                                <chmod file="${project.build.directory}/${standalone.finalName}/bin/start-server.sh" perm="755"/>
                                
                                <!-- 创建 README.txt 说明文件 -->
                                <echo file="${project.build.directory}/${standalone.finalName}/README.txt">文件传输服务端独立部署包${jar.classifier.suffix}
===========================

## 目录结构说明

- ${standalone.finalName}.jar    主应用程序（依赖分离）
- lib/                          依赖库目录
- config/                       配置文件目录
- bin/                          启动脚本目录
- data/                         数据存储目录
- logs/                         日志文件目录
- temp/                         临时文件目录
- docs/                         文档目录

## 快速启动

### Linux/Mac 系统
```bash
./bin/start-server.sh start
```

### Windows 系统
```powershell
powershell -ExecutionPolicy Bypass -File .\bin\start-server.ps1 start
```

## 启动脚本功能

支持以下命令：
- start     启动服务器
- stop      停止服务器
- restart   重启服务器
- status    查看服务器状态
- logs      查看服务器日志

启动选项：
- -Port PORT           指定服务器端口（默认: 49011）
- -Profile PROFILE     指定Spring配置文件（默认: server）
- -Background          后台运行服务器
- -Help                显示帮助信息

## 智能路径查找

启动脚本具备智能路径查找功能：
✅ 部署环境：自动从 bin/ 目录找到上级目录的JAR文件
✅ 开发环境：自动查找 target/ 目录中的JAR文件
✅ 配置文件：自动检测 ../config/, config/, src/main/resources/ 等路径
✅ 详细错误信息：提供清晰的路径搜索和错误提示

## 服务验证

启动后访问以下URL验证服务：
- 健康检查：http://localhost:49011/filetransfer/actuator/health
- API文档：http://localhost:49011/filetransfer/doc.html

## 配置文件

主配置文件：config/file-transfer-server.yml
支持自动更新机制，文件权限已优化。

## 系统要求

- Java ${java.target.version}+ 运行环境
- 最小内存：512MB
- 推荐内存：1GB
- 磁盘空间：根据传输文件大小确定

## 注意事项

1. 首次运行前请检查配置文件中的用户密钥和存储路径
2. 默认服务端口为 49011，如有冲突请修改配置
3. 数据文件默认存储在 data/ 目录下
4. 支持SQLite数据库故障容错机制
5. 具备完整的日志记录和监控功能

## 故障排除

如遇到问题，请查看：
1. logs/server.log - 服务器运行日志
2. logs/server-error.log - 错误日志
3. docs/DEPLOYMENT_GUIDE.md - 详细部署指南

版本：${project.version}${jar.classifier.suffix}
构建时间：${maven.build.timestamp}
目标Java版本：${java.target.version}
</echo>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- ================================================= -->
            <!-- 5. Maven Assembly Plugin - 创建分发压缩包（可选） -->
            <!-- ================================================= -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>create-distribution</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>src/assembly/distribution.xml</descriptor>
                            </descriptors>
                            <finalName>${standalone.finalName}</finalName>
                            <appendAssemblyId>true</appendAssemblyId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- ======================================== -->
            <!-- 6. Maven Compiler Plugin - 编译配置     -->
            <!-- ======================================== -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.target.version}</source>
                    <target>${java.target.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <release>${java.release.version}</release>
                    <compilerArgs>
                        <arg>--release</arg>
                        <arg>${java.release.version}</arg>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- ================================================ -->
            <!-- 7. Spring Boot Maven Plugin - 保留原有功能     -->
            <!-- ================================================ -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                    <mainClass>${standalone.mainClass}</mainClass>
                    <!-- 生成独立的可执行JAR -->
                    <classifier>boot${jar.classifier.suffix}</classifier>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project> 