package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.nio.file.Path;
import java.time.Instant;

import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.sdesrd.filetransfer.server.dto.FileMetadata;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;

/**
 * FileMetadataService 单元测试类
 * 
 * <p>测试文件元数据服务的核心功能，包括info.json文件的创建、读取、更新和删除。
 * 验证在不同场景下元数据管理的正确性和可靠性。</p>
 * 
 */
class FileMetadataServiceTest {
    
    /**
     * 临时目录，用于测试文件操作
     */
    @TempDir
    Path tempDir;
    
    /**
     * 被测试的服务实例
     */
    private FileMetadataService metadataService;
    
    /**
     * 测试用的存储路径
     */
    private String testStoragePath;
    
    /**
     * 测试用的文件ID
     */
    private static final String TEST_FILE_ID = "01HN2Z8X9K7Q3M5P6R8S9T0V1W";
    
    /**
     * 测试用的原始文件名
     */
    private static final String TEST_ORIGINAL_FILENAME = "测试文档.pdf";
    
    /**
     * 测试用的物理文件名
     */
    private static final String TEST_PHYSICAL_FILENAME = "d41d8cd98f00b204e9800998ecf8427e.pdf";
    
    /**
     * 测试用的文件大小
     */
    private static final Long TEST_FILE_SIZE = 1024000L;
    
    /**
     * 测试用的MD5值
     */
    private static final String TEST_MD5 = "d41d8cd98f00b204e9800998ecf8427e";
    
    @BeforeEach
    void setUp() {
        metadataService = new FileMetadataService();
        testStoragePath = tempDir.toString();
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试文件
        try {
            File storageDir = new File(testStoragePath);
            if (storageDir.exists()) {
                deleteDirectory(storageDir);
            }
        } catch (Exception e) {
            // 忽略清理错误
        }
    }
    
    /**
     * 测试创建元数据文件
     */
    @Test
    void testCreateMetadata() {
        // 准备测试数据
        FileTransferRecord record = createTestRecord();
        
        // 执行创建操作
        assertDoesNotThrow(() -> {
            metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, TEST_ORIGINAL_FILENAME, testStoragePath);
        });
        
        // 验证元数据文件是否存在
        assertTrue(metadataService.metadataExists(TEST_FILE_ID, testStoragePath));
        
        // 验证元数据内容
        FileMetadata metadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
        assertNotNull(metadata);
        assertEquals(TEST_FILE_ID, metadata.getFileId());
        assertEquals(TEST_ORIGINAL_FILENAME, metadata.getOriginalFileName());
        assertEquals(TEST_PHYSICAL_FILENAME, metadata.getPhysicalFileName());
        assertEquals(TEST_FILE_SIZE, metadata.getFileSize());
        assertEquals("1.0.0", metadata.getVersion());
    }
    
    /**
     * 测试读取元数据文件
     */
    @Test
    void testReadMetadata() {
        // 先创建元数据文件
        FileTransferRecord record = createTestRecord();
        metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, TEST_ORIGINAL_FILENAME, testStoragePath);
        
        // 读取元数据
        FileMetadata metadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
        
        // 验证读取结果
        assertNotNull(metadata);
        assertEquals(TEST_FILE_ID, metadata.getFileId());
        assertEquals(TEST_ORIGINAL_FILENAME, metadata.getOriginalFileName());
        assertEquals(TEST_PHYSICAL_FILENAME, metadata.getPhysicalFileName());
        assertEquals(TEST_FILE_SIZE, metadata.getFileSize());
        assertEquals(2, metadata.getStatus()); // 传输完成状态
    }
    
    /**
     * 测试更新元数据文件
     */
    @Test
    void testUpdateMetadata() {
        // 先创建元数据文件
        FileTransferRecord record = createTestRecord();
        metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, TEST_ORIGINAL_FILENAME, testStoragePath);
        
        // 修改记录并更新
        record.setStatus(3); // 设置为失败状态
        record.setFailReason("测试失败原因");
        String newOriginalFileName = "更新后的文件名.pdf";
        
        assertDoesNotThrow(() -> {
            metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, newOriginalFileName, testStoragePath);
        });
        
        // 验证更新结果
        FileMetadata updatedMetadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
        assertNotNull(updatedMetadata);
        assertEquals(newOriginalFileName, updatedMetadata.getOriginalFileName());
        assertEquals(3, updatedMetadata.getStatus());
    }
    
    /**
     * 测试删除元数据文件
     */
    @Test
    void testDeleteMetadata() {
        // 先创建元数据文件
        FileTransferRecord record = createTestRecord();
        metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, TEST_ORIGINAL_FILENAME, testStoragePath);
        
        // 验证文件存在
        assertTrue(metadataService.metadataExists(TEST_FILE_ID, testStoragePath));
        
        // 删除元数据文件
        boolean deleted = metadataService.deleteMetadata(TEST_FILE_ID, testStoragePath);
        
        // 验证删除结果
        assertTrue(deleted);
        assertFalse(metadataService.metadataExists(TEST_FILE_ID, testStoragePath));
        
        // 验证读取返回null
        FileMetadata metadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
        assertNull(metadata);
    }
    
    /**
     * 测试元数据文件不存在的情况
     */
    @Test
    void testReadNonExistentMetadata() {
        // 读取不存在的元数据文件
        FileMetadata metadata = metadataService.readMetadata("non-existent-id", testStoragePath);
        
        // 验证返回null
        assertNull(metadata);
        
        // 验证存在性检查返回false
        assertFalse(metadataService.metadataExists("non-existent-id", testStoragePath));
    }
    
    /**
     * 测试无效参数的处理
     */
    @Test
    void testInvalidParameters() {
        FileTransferRecord record = createTestRecord();
        
        // 测试空fileId
        assertThrows(FileTransferException.class, () -> {
            metadataService.createOrUpdateMetadata("", record, TEST_ORIGINAL_FILENAME, testStoragePath);
        });
        
        assertThrows(FileTransferException.class, () -> {
            metadataService.createOrUpdateMetadata(null, record, TEST_ORIGINAL_FILENAME, testStoragePath);
        });
        
        // 测试空record
        assertThrows(FileTransferException.class, () -> {
            metadataService.createOrUpdateMetadata(TEST_FILE_ID, null, TEST_ORIGINAL_FILENAME, testStoragePath);
        });
    }
    
    /**
     * 测试并发访问安全性
     */
    @Test
    void testConcurrentAccess() throws InterruptedException {
        FileTransferRecord record = createTestRecord();
        
        // 创建多个线程同时操作同一个文件
        Thread[] threads = new Thread[5];
        for (int i = 0; i < threads.length; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    String originalFileName = "并发测试文件_" + threadIndex + ".txt";
                    metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, originalFileName, testStoragePath);
                    
                    // 读取验证
                    FileMetadata metadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
                    assertNotNull(metadata);
                    
                } catch (Exception e) {
                    fail("并发操作失败: " + e.getMessage());
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000); // 最多等待5秒
        }
        
        // 验证最终文件存在
        assertTrue(metadataService.metadataExists(TEST_FILE_ID, testStoragePath));
    }
    
    /**
     * 测试UTF-8编码处理
     */
    @Test
    void testUtf8Encoding() {
        // 使用包含中文字符的文件名
        String chineseFileName = "中文测试文件名_测试编码.txt";
        
        FileTransferRecord record = createTestRecord();
        record.setFileName("encoded_file.txt");
        
        // 创建元数据
        assertDoesNotThrow(() -> {
            metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, chineseFileName, testStoragePath);
        });
        
        // 读取并验证中文字符正确保存
        FileMetadata metadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
        assertNotNull(metadata);
        assertEquals(chineseFileName, metadata.getOriginalFileName());
    }
    
    /**
     * 测试大文件元数据处理
     */
    @Test
    void testLargeFileMetadata() {
        // 创建大文件记录
        FileTransferRecord record = createTestRecord();
        record.setFileSize(10L * 1024 * 1024 * 1024); // 10GB
        record.setTotalChunks(10240); // 10240个分块
        record.setCompletedChunks(10240);
        
        String largeFileName = "大文件测试_10GB.bin";
        
        // 创建元数据
        assertDoesNotThrow(() -> {
            metadataService.createOrUpdateMetadata(TEST_FILE_ID, record, largeFileName, testStoragePath);
        });
        
        // 验证大文件信息正确保存
        FileMetadata metadata = metadataService.readMetadata(TEST_FILE_ID, testStoragePath);
        assertNotNull(metadata);
        assertEquals(largeFileName, metadata.getOriginalFileName());
        assertEquals(10L * 1024 * 1024 * 1024, metadata.getFileSize());
        assertEquals(10240, metadata.getTotalChunks());
        assertEquals(10240, metadata.getCompletedChunks());
        assertTrue(metadata.isComplete());
    }
    
    /**
     * 创建测试用的文件传输记录
     * 
     * @return 测试记录
     */
    private FileTransferRecord createTestRecord() {
        FileTransferRecord record = new FileTransferRecord();
        record.setId("test-transfer-id");
        record.setFileId(TEST_FILE_ID);
        record.setFileName(TEST_PHYSICAL_FILENAME);
        record.setOriginalFileName(TEST_ORIGINAL_FILENAME);
        record.setFileSize(TEST_FILE_SIZE);
        record.setFilePath(testStoragePath + "/202412/" + TEST_FILE_ID + "/" + TEST_PHYSICAL_FILENAME);
        record.setTransferredSize(TEST_FILE_SIZE);
        record.setTotalChunks(10);
        record.setCompletedChunks(10);
        record.setStatus(2); // 传输完成
        record.setClientIp("127.0.0.1");
        record.setCreateTime(Instant.now().toString());
        record.setUpdateTime(Instant.now().toString());
        record.setCompleteTime(Instant.now().toString());
        record.setFileType("application/pdf");
        record.setExtInfo("{\"originalMd5\":\"" + TEST_MD5 + "\",\"fileExtension\":\"pdf\"}");
        
        return record;
    }
    
    /**
     * 递归删除目录及其内容
     * 
     * @param directory 要删除的目录
     */
    private void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }
} 