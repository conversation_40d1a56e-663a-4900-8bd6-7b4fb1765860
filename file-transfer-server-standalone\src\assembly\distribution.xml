<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    
    <id>dist</id>
    
    <!-- 输出格式：同时生成 zip 和 tar.gz 两种格式 -->
    <formats>
        <format>zip</format>
        <format>tar.gz</format>
    </formats>
    
    <!-- 不包含基础目录名称，直接从内容开始 -->
    <includeBaseDirectory>false</includeBaseDirectory>
    
    <!-- 定义文件集合 -->
    <fileSets>
        <!-- 1. 包含整个分发目录结构 -->
        <fileSet>
            <directory>${project.build.directory}/${standalone.finalName}</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>**/*</include>
            </includes>
            <!-- 使用默认文件权限，支持自动更新机制 -->
            <directoryMode>0755</directoryMode>
        </fileSet>
        
        <!-- 2. 单独设置启动脚本的执行权限 -->
        <fileSet>
            <directory>${project.build.directory}/${standalone.finalName}/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>*.sh</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>
    
</assembly> 