package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 上传结果
 */
@Data
public class UploadResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 传输ID
     */
    private String transferId;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 是否秒传
     */
    private boolean fastUpload;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 文件相对路径（相对于存储根目录）
     * 格式：YYYYMM/fileId/fileName
     */
    private String relativePath;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 上传完成时间（ISO格式）
     */
    private String completeTime;

    /**
     * 传输耗时（毫秒）
     */
    private Long transferDuration;
}