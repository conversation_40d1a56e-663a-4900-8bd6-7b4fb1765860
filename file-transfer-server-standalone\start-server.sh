#!/bin/bash

# ================================================================================
# 文件传输独立服务端启动脚本
# ================================================================================

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输独立服务端启动脚本"

# 服务配置
readonly DEFAULT_SERVER_PORT=49011
readonly DEFAULT_PROFILE="server"
readonly DEFAULT_CONFIG_FILE="application.yml"

# JAR文件配置 - 支持多版本JAR文件名
readonly JAR_BASE_NAME="file-transfer-server-standalone-1.0.0"
readonly TARGET_DIR="target"

# 日志配置
readonly LOG_DIR="logs"
readonly PID_FILE="$LOG_DIR/server.pid"
readonly LOG_FILE="$LOG_DIR/server.log"

# 超时配置
readonly STARTUP_TIMEOUT=60  # 服务器启动超时时间（秒）
readonly SHUTDOWN_TIMEOUT=30 # 服务器关闭超时时间（秒）

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
}

# 成功日志
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
}

# 警告日志
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
}

# 错误日志
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
}

# ==================== 工具函数 ====================

# 显示脚本头部信息
show_header() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================================"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start     启动服务器"
    echo "  stop      停止服务器"
    echo "  restart   重启服务器"
    echo "  status    查看服务器状态"
    echo "  logs      查看服务器日志"
    echo ""
    echo "选项:"
    echo "  --port PORT           指定服务器端口 (默认: $DEFAULT_SERVER_PORT)"
    echo "  --profile PROFILE     指定Spring配置文件 (默认: $DEFAULT_PROFILE)"
    echo "  --config CONFIG       指定配置文件路径 (默认: $DEFAULT_CONFIG_FILE)"
    echo "  --java-home PATH      指定Java JDK路径 (可选，默认使用系统Java)"
    echo "  --background          后台运行服务器"
    echo "  --help                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                                          # 启动服务器"
    echo "  $0 start --port 8080                              # 在端口8080启动服务器"
    echo "  $0 start --background                             # 后台启动服务器"
    echo "  $0 start --java-home /opt/java/jdk21              # 使用指定的Java启动服务器"
    echo "  $0 start --java-home '/opt/my java/jdk with space' # 支持包含空格的路径（需要引号）"
    echo "  $0 stop                                           # 停止服务器"
    echo "  $0 status                                         # 查看服务器状态"
    echo ""
}

# 初始化日志目录
init_logging() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        log_info "创建日志目录：$LOG_DIR"
    fi
}

# 设置Java环境
setup_java_environment() {
    local custom_java_home="$1"
    
    # 如果指定了自定义Java路径，则使用它
    if [ -n "$custom_java_home" ]; then
        if [ -d "$custom_java_home" ] && [ -x "$custom_java_home/bin/java" ]; then
            export JAVA_HOME="$custom_java_home"
            export PATH="$custom_java_home/bin:$PATH"
            log_info "使用指定的Java JDK：$custom_java_home"
        else
            log_error "指定的Java JDK路径无效：$custom_java_home"
            return 1
        fi
    else
        # 使用系统默认Java环境
        if [ -n "$JAVA_HOME" ] && [ -d "$JAVA_HOME" ] && [ -x "$JAVA_HOME/bin/java" ]; then
            log_info "使用系统JAVA_HOME环境变量：$JAVA_HOME"
        else
            log_info "使用系统默认Java环境"
        fi
    fi
    
    # 验证Java命令可用性
    if ! command -v java &> /dev/null; then
        log_error "Java运行时未安装或未在PATH中"
        return 1
    fi
    
    # 获取Java版本信息
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "当前Java版本：$java_version"
    
    return 0
}

# 检查JAR文件是否存在 - 智能查找多版本JAR文件
check_jar_file() {
    # 定义可能的JAR文件名（优先查找主JAR包）
    local jar_names=(
        "${JAR_BASE_NAME}.jar"  # 主JAR包 (Java 8)
        "${JAR_BASE_NAME}-java11.jar"
        "${JAR_BASE_NAME}-java17.jar"
        "${JAR_BASE_NAME}-java21.jar"
    )
    
    # 定义可能的JAR文件路径
    local jar_paths=()
    for jar_name in "${jar_names[@]}"; do
        jar_paths+=(
            # 1. 部署环境：JAR文件在bin目录的上级目录
            "../$jar_name"
            # 2. 开发环境：JAR文件在target分发目录（优先，因为有正确的lib依赖）
            "$TARGET_DIR/${jar_name%.jar}/$jar_name"
            # 3. 开发环境：JAR文件在target目录（但可能缺少依赖路径）
            "$TARGET_DIR/$jar_name"
            # 4. 当前目录（如果直接在根目录运行）
            "$jar_name"
        )
    done
    
    for jar_path in "${jar_paths[@]}"; do
        if [ -f "$jar_path" ]; then
            log_info "找到JAR文件：$jar_path"
            
            # 检查对应的lib目录是否存在（用于验证依赖完整性）
            local jar_dir=$(dirname "$jar_path")
            # 处理相对路径为空的情况（如当前目录的JAR文件）
            if [ "$jar_dir" = "." ] || [ -z "$jar_dir" ]; then
                jar_dir="."
            fi
            local lib_dir="$jar_dir/lib"
            
            if [ -d "$lib_dir" ]; then
                local lib_count=$(find "$lib_dir" -name "*.jar" | wc -l)
                log_info "检测到依赖库目录：$lib_dir ($lib_count 个JAR文件)"
                CURRENT_JAR_PATH="$jar_path"
                return 0
            else
                log_warning "JAR文件存在但缺少依赖库目录：$lib_dir"
                # 继续检查其他路径
            fi
        fi
    done
    
    log_error "未找到有效的JAR文件（包含依赖库）"
    log_info "已搜索以下JAR文件名："
    for jar_name in "${jar_names[@]}"; do
        log_info "  - $jar_name"
    done
    log_info ""
    log_info "已搜索以下路径模式："
    log_info "  - ../[JAR文件名] （部署环境）"
    log_info "  - target/[JAR目录]/[JAR文件名] （开发环境分发目录）"
    log_info "  - target/[JAR文件名] （开发环境target目录）"
    log_info "  - [JAR文件名] （当前目录）"
    echo ""
    log_info "请确认："
    log_info "  1. 如果是部署环境，请确保JAR文件在上级目录"
    log_info "  2. 如果是开发环境，请先运行构建命令："
    log_info "     cd .. && ./build-and-test.sh build"
    log_info "  3. 确保lib目录与JAR文件在同一目录下"
    log_info "  4. 检查构建输出目录结构是否正确"
    
    return 1
}

# 检查端口是否被占用
check_port() {
    local port="$1"
    
    if lsof -ti:$port >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用"
        return 1
    fi
    
    return 0
}

# 获取服务器PID
get_server_pid() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "$pid"
            return 0
        else
            # PID文件存在但进程不存在，删除PID文件
            rm -f "$PID_FILE"
        fi
    fi
    
    return 1
}

# 启动服务器
start_server() {
    local port="$1"
    local profile="$2"
    local config_file="$3"
    local background="$4"
    
    log_info "启动文件传输服务器..."
    
    # 检查服务器是否已经运行
    if get_server_pid >/dev/null; then
        local pid=$(get_server_pid)
        log_warning "服务器已经在运行 (PID: $pid)"
        return 1
    fi
    
    # 检查端口
    if ! check_port "$port"; then
        log_error "无法启动服务器，端口 $port 已被占用"
        return 1
    fi
    
    # 检查JAR文件
    if ! check_jar_file; then
        return 1
    fi
    
    # 构建Java命令 - 使用check_jar_file函数找到的路径
    local jar_path="$CURRENT_JAR_PATH"
    local java_opts="-Xms512m -Xmx1g -XX:+UseG1GC"
    local spring_opts="--server.port=$port --spring.profiles.active=$profile"
    
    # 智能处理配置文件路径
    if [ -n "$config_file" ] && [ "$config_file" != "$DEFAULT_CONFIG_FILE" ]; then
        spring_opts="$spring_opts --spring.config.location=classpath:/$config_file"
    else
        # 为部署环境添加配置文件路径
        local config_paths=(
            "../config/"           # 部署环境：config目录在上级目录
            "config/"              # 开发环境：config目录在当前目录
            "src/main/resources/"  # 开发环境：Maven资源目录
        )
        
        local valid_config_paths=()
        for config_path in "${config_paths[@]}"; do
            if [ -d "$config_path" ]; then
                valid_config_paths+=("file:$config_path")
            fi
        done
        
        if [ ${#valid_config_paths[@]} -gt 0 ]; then
            local config_location=$(IFS=','; echo "${valid_config_paths[*]}")
            spring_opts="$spring_opts --spring.config.location=$config_location"
            log_info "配置文件搜索路径：$config_location"
        fi
    fi
    
    # 安全处理包含空格的JAR路径：使用数组来正确处理参数
    local java_args=()
    IFS=' ' read -ra java_opts_array <<< "$java_opts"
    java_args+=("${java_opts_array[@]}")
    java_args+=("-jar" "$jar_path")
    IFS=' ' read -ra spring_opts_array <<< "$spring_opts"
    java_args+=("${spring_opts_array[@]}")
    
    log_info "Java命令：java ${java_args[*]}"
    
    if [ "$background" = true ]; then
        # 后台启动 - 使用数组参数确保正确处理空格
        nohup java "${java_args[@]}" > "$LOG_FILE" 2>&1 &
        local pid=$!
        echo $pid > "$PID_FILE"
        
        log_info "服务器后台启动中 (PID: $pid)..."
        
        # 等待服务器启动
        local wait_count=0
        while [ $wait_count -lt $STARTUP_TIMEOUT ]; do
            # 注意：由于设置了context-path为/filetransfer，健康检查路径需要包含前缀
            if curl -s "http://localhost:$port/filetransfer/actuator/health" >/dev/null 2>&1; then
                log_success "服务器启动成功！"
                log_info "服务器地址: http://localhost:$port"
                log_info "API文档: http://localhost:$port/filetransfer/doc.html"
                log_info "健康检查: http://localhost:$port/filetransfer/actuator/health"
                log_info "日志文件: $LOG_FILE"
                log_info "PID文件: $PID_FILE"
                return 0
            fi
            
            sleep 1
            wait_count=$((wait_count + 1))
            
            # 检查进程是否还在运行
            if ! kill -0 $pid 2>/dev/null; then
                log_error "服务器进程意外退出"
                rm -f "$PID_FILE"
                return 1
            fi
        done
        
        log_error "服务器启动超时"
        stop_server
        return 1
    else
        # 前台启动
        log_info "服务器前台启动中..."
        log_info "按 Ctrl+C 停止服务器"
        
        # 定义清理函数
        cleanup_foreground() {
            log_info "收到停止信号，正在关闭服务器..."
            if [ -n "$JAVA_PID" ] && kill -0 "$JAVA_PID" 2>/dev/null; then
                kill "$JAVA_PID" 2>/dev/null || true
                wait "$JAVA_PID" 2>/dev/null || true
            fi
            # 删除PID文件
            rm -f "$PID_FILE"
            log_info "服务器已停止，PID文件已清理"
            exit 0
        }
        
        # 设置信号处理
        trap cleanup_foreground INT TERM
        
        # 启动Java进程（不使用exec，以便获取PID）- 使用数组参数确保正确处理空格
        java "${java_args[@]}" &
        JAVA_PID=$!
        
        # 将PID写入文件
        echo $JAVA_PID > "$PID_FILE"
        log_info "服务器前台启动完成 (PID: $JAVA_PID)"
        log_info "PID文件已创建: $PID_FILE"
        
        # 等待Java进程结束
        wait $JAVA_PID
        
        # 正常结束时也清理PID文件
        rm -f "$PID_FILE"
        log_info "服务器进程结束，PID文件已清理"
    fi
}

# 停止服务器
stop_server() {
    log_info "停止文件传输服务器..."
    
    local pid
    if ! pid=$(get_server_pid); then
        log_warning "服务器未运行"
        return 0
    fi
    
    log_info "停止服务器进程 (PID: $pid)..."
    
    # 发送TERM信号
    kill $pid 2>/dev/null || true
    
    # 等待进程退出
    local wait_count=0
    while [ $wait_count -lt $SHUTDOWN_TIMEOUT ]; do
        if ! kill -0 $pid 2>/dev/null; then
            rm -f "$PID_FILE"
            log_success "服务器已停止"
            return 0
        fi
        
        sleep 1
        wait_count=$((wait_count + 1))
    done
    
    # 强制杀死进程
    log_warning "强制停止服务器进程..."
    kill -9 $pid 2>/dev/null || true
    rm -f "$PID_FILE"
    
    log_success "服务器已强制停止"
    return 0
}

# 查看服务器状态
show_status() {
    local pid
    if pid=$(get_server_pid); then
        log_success "服务器正在运行 (PID: $pid)"
        
        # 尝试获取服务器信息
        local port=$(ps -p $pid -o args= | grep -o '\--server\.port=[0-9]*' | cut -d'=' -f2)
        if [ -z "$port" ]; then
            port=$DEFAULT_SERVER_PORT
        fi
        
        echo "服务器信息:"
        echo "  PID: $pid"
        echo "  端口: $port"
        echo "  服务器地址: http://localhost:$port"
        echo "  API文档: http://localhost:$port/filetransfer/doc.html"
        echo "  健康检查: http://localhost:$port/filetransfer/actuator/health"
        echo "  日志文件: $LOG_FILE"
        echo "  PID文件: $PID_FILE"

        # 检查健康状态
        if curl -s "http://localhost:$port/filetransfer/actuator/health" >/dev/null 2>&1; then
            log_success "服务器健康检查通过"
        else
            log_warning "服务器健康检查失败"
        fi
    else
        log_info "服务器未运行"
    fi
}

# 查看服务器日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "显示服务器日志：$LOG_FILE"
        echo "========================================"
        tail -f "$LOG_FILE"
    else
        log_warning "日志文件不存在：$LOG_FILE"
    fi
}

# 重启服务器
restart_server() {
    local port="$1"
    local profile="$2"
    local config_file="$3"
    local background="$4"
    
    log_info "重启文件传输服务器..."
    
    # 停止服务器
    stop_server
    
    # 等待一秒
    sleep 1
    
    # 启动服务器
    start_server "$port" "$profile" "$config_file" "$background"
}

# ==================== 主程序 ====================

# 主函数
main() {
    # 显示脚本头部信息
    show_header
    
    # 初始化日志
    init_logging
    
    # 解析命令行参数
    local command=""
    local port="$DEFAULT_SERVER_PORT"
    local profile="$DEFAULT_PROFILE"
    local config_file="$DEFAULT_CONFIG_FILE"
    local java_home=""
    local background=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            start|stop|restart|status|logs)
                command="$1"
                shift
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --profile)
                profile="$2"
                shift 2
                ;;
            --config)
                config_file="$2"
                shift 2
                ;;
            --java-home)
                java_home="$2"
                shift 2
                ;;
            --background)
                background=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定命令，显示帮助
    if [ -z "$command" ]; then
        show_help
        exit 1
    fi
    
    # 设置Java环境
    if ! setup_java_environment "$java_home"; then
        exit 1
    fi
    
    # 执行命令
    case $command in
        start)
            start_server "$port" "$profile" "$config_file" "$background"
            ;;
        stop)
            stop_server
            ;;
        restart)
            restart_server "$port" "$profile" "$config_file" "$background"
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        *)
            log_error "未知命令: $command"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
