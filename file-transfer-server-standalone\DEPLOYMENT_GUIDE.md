# File Transfer Server Standalone 独立部署指南

## 概述

本指南介绍如何使用新配置的 Maven 构建插件来创建文件传输服务端的独立部署包。新的构建配置实现了依赖分离打包，使得部署和维护更加便捷。

## 构建配置功能

### 1. 依赖分离打包
- **maven-jar-plugin**: 生成不包含依赖的主应用 JAR 包
- **maven-dependency-plugin**: 将所有运行时依赖复制到 `lib/` 目录
- **MANIFEST.MF 配置**: 正确设置 Class-Path 指向 `lib/` 目录

### 2. 资源文件处理
- **maven-resources-plugin**: 智能复制配置文件、启动脚本和文档
- **配置文件过滤**: 排除开发环境特定文件，保留运行时必需配置
- **变量替换**: 支持 Maven 变量在资源文件中的替换

### 3. 完整目录结构
- 创建标准的企业级应用部署目录结构
- 包含所有必要的运行时目录（data、logs、temp等）
- 提供详细的部署说明文档

### 4. 双格式输出
- **目录格式**: 直接可用的部署目录
- **压缩包格式**: ZIP 和 TAR.GZ 分发包

## 构建命令

### 使用项目统一构建脚本（推荐）

```bash
# Linux/Mac - 构建默认Java 8版本
./build-and-test.sh build --module file-transfer-server-standalone

# 构建指定Java版本
./build-and-test.sh build --java-version java8 --module file-transfer-server-standalone
./build-and-test.sh build --java-version java11 --module file-transfer-server-standalone
./build-and-test.sh build --java-version java17 --module file-transfer-server-standalone
./build-and-test.sh build --java-version java21 --module file-transfer-server-standalone

# Windows - 构建默认Java 8版本
.\build-and-test.ps1 -Mode build -Module file-transfer-server-standalone

# 构建指定Java版本
.\build-and-test.ps1 -Mode build -JavaVersion java8 -Module file-transfer-server-standalone
.\build-and-test.ps1 -Mode build -JavaVersion java11 -Module file-transfer-server-standalone
.\build-and-test.ps1 -Mode build -JavaVersion java17 -Module file-transfer-server-standalone
.\build-and-test.ps1 -Mode build -JavaVersion java21 -Module file-transfer-server-standalone
```

### 直接使用 Maven 命令

```bash
# 进入 standalone 模块目录
cd file-transfer-server-standalone

# 清理并构建
mvn clean package

# 或者只构建 standalone 模块（从项目根目录）
mvn clean package -pl file-transfer-server-standalone -am
```

## 构建输出

### 多版本构建说明

项目支持多Java版本构建，每个版本会生成带有版本后缀的JAR文件：
- **java8**: 适合传统环境，最大兼容性
- **java11**: LTS版本，推荐用于生产环境
- **java17**: LTS版本，现代Java特性支持
- **java21**: 最新LTS版本，最佳性能

### 构建产物

构建完成后，在 `target/` 目录下会生成以下文件（以Java 8版本为例）：

```
target/
├── file-transfer-server-standalone-1.0.0-java8.jar           # 主应用JAR（依赖分离，带版本后缀）
├── file-transfer-server-standalone-1.0.0-java8-boot.jar     # Spring Boot 可执行JAR（包含依赖）
├── file-transfer-server-standalone-1.0.0-java8-dist.zip     # ZIP 分发包
├── file-transfer-server-standalone-1.0.0-java8-dist.tar.gz  # TAR.GZ 分发包
└── file-transfer-server-standalone-1.0.0-java8/             # 部署目录结构
    ├── file-transfer-server-standalone-1.0.0-java8.jar      # 主应用程序
    ├── lib/                                            # 依赖库目录
    │   ├── spring-boot-starter-2.6.6.jar
    │   ├── file-transfer-server-sdk-1.0.0-java8.jar
    │   └── ...                                         # 其他依赖JAR
    ├── config/                                         # 配置文件目录
    │   └── file-transfer-server.yml                    # 主配置文件
    ├── bin/                                            # 启动脚本目录
    │   ├── start-server.sh                             # Linux/Mac 启动脚本
    │   └── start-server.ps1                            # Windows 启动脚本
    ├── data/                                           # 数据存储目录
    ├── logs/                                           # 日志文件目录
    ├── temp/                                           # 临时文件目录
    ├── docs/                                           # 文档目录
    │   ├── README.md                                   # 项目说明
    │   └── integration-guide.md                        # 集成指南
    └── README.txt                                      # 部署说明文件
```

## 部署和运行

### 版本选择建议

选择的JAR版本应匹配或低于目标运行环境的Java版本：

| 构建版本 | 运行环境要求 | 适用场景 |
|----------|-------------|----------|
| java8    | Java 8+     | 传统环境，最大兼容性 |
| java11   | Java 11+    | 生产环境推荐 |
| java17   | Java 17+    | 现代化部署 |
| java21   | Java 21+    | 最新环境，最佳性能 |

### 1. 解压分发包

```bash
# 解压 ZIP 包
unzip file-transfer-server-standalone-1.0.0-java8-dist.zip

# 或解压 TAR.GZ 包
tar -xzf file-transfer-server-standalone-1.0.0-java8-dist.tar.gz
```

### 2. 配置服务

编辑 `config/file-transfer-server.yml` 文件，根据实际环境调整配置：

```yaml
server:
  port: 49011

file:
  transfer:
    server:
      database-path: ./data/file-transfer/database.db
      users:
        admin:
          secret-key: "your-admin-secret-key"
          storage-path: ./data/admin/files
        # 其他用户配置...
```

### 3. 启动服务

#### Linux/Mac 系统

```bash
./bin/start-server.sh start
```

#### Windows 系统

```powershell
powershell -ExecutionPolicy Bypass -File .\bin\start-server.ps1 start
```

#### 直接使用 Java 命令

```bash
# 在部署根目录下执行
java -jar file-transfer-server-standalone-1.0.0-java8.jar --spring.config.location=file:./config/
```

### 4. 验证服务

访问以下 URL 验证服务是否正常启动：

```
http://localhost:49011/api/file/health
```

期望响应：
```json
{
  "code": 200,
  "message": "服务运行正常",
  "data": {
    "status": "UP",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

## 依赖管理

### Class-Path 配置

主 JAR 包的 `MANIFEST.MF` 文件包含正确的 Class-Path 配置：

```
Class-Path: lib/spring-boot-starter-2.6.6.jar lib/file-transfer-server-sdk-1.0.0-java8.jar ...
Main-Class: com.sdesrd.filetransfer.server.standalone.FileTransferServerApplication
```

### 依赖更新

如需更新依赖版本：

1. 修改 `pom.xml` 中的依赖版本
2. 重新执行构建命令
3. 替换部署目录中的 `lib/` 文件夹

## 故障排除

**路径智能查找功能**：启动脚本已经过优化，现在支持：
- ✅ **部署环境**：自动从 `bin/` 目录找到上级目录的JAR文件
- ✅ **开发环境**：自动查找 `target/` 目录中的JAR文件
- ✅ **配置文件**：自动检测 `../config/`, `config/`, `src/main/resources/` 等路径
- ✅ **详细错误信息**：提供清晰的路径搜索和错误提示

### 常见问题

1. **权限不足错误**
   ```bash
   chmod +x bin/start-server.sh
   ```

2. **找不到主类错误**
   - 检查 `lib/` 目录是否包含所有依赖
   - 验证 JAR 包的 MANIFEST.MF 文件

3. **配置文件加载失败**
   - 确保 `config/` 目录中有 `file-transfer-server.yml`
   - 检查配置文件格式是否正确

4. **端口冲突**
   - 修改 `config/file-transfer-server.yml` 中的端口配置
   - 或在启动时指定：`--server.port=8080`

### 日志查看

```bash
# 查看启动日志
tail -f logs/file-transfer-server.log

# 查看错误日志
tail -f logs/error.log
```

## 生产环境建议

1. **资源配置**: 根据预期负载调整 JVM 内存参数
2. **安全配置**: 更改默认的 secret-key 和端口
3. **备份策略**: 定期备份 `data/` 目录
4. **监控设置**: 配置应用监控和告警
5. **防火墙**: 确保必要端口的网络可达性
