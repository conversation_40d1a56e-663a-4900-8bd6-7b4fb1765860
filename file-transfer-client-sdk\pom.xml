<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sdesrd.filetransfer</groupId>
        <artifactId>file-transfer-sdk-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>file-transfer-client-sdk</artifactId>
    <packaging>jar</packaging>
    <name>文件传输客户端SDK</name>
    <description>文件传输客户端SDK，支持断点续传和传输限速</description>

    <dependencies>
        <!-- OkHttp HTTP客户端 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <!-- JSON处理 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!-- Apache Commons IO -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!-- Apache Commons Lang -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- Hutool工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>

        <!-- SLF4J日志接口 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 测试依赖 - JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Logback实现(可选) -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}${jar.classifier.suffix}</finalName>
        
        <plugins>
            <!-- 不生成可执行jar，只生成library jar -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- Maven Surefire Plugin for JUnit 5 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/integration/**/*Test.java</exclude>
                        <exclude>**/*IntegrationTest.java</exclude>
                        <exclude>**/*EndToEndTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
    <!-- 多版本构建profiles -->
    <profiles>
        <!-- Java 8构建 - 在Java 21环境下运行时需要额外的模块访问配置 -->
        <profile>
            <id>java8</id>
            <properties>
                <java.target.version>8</java.target.version>
                <java.release.version>8</java.release.version>
                <jar.classifier.suffix>-java8</jar.classifier.suffix>
                <jar.classifier>java8</jar.classifier>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- 在Java 21环境下运行Java 8构建时需要的模块访问权限 -->
                            <argLine>
                                --add-opens java.base/java.lang=ALL-UNNAMED
                                --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                                --add-opens java.base/java.util=ALL-UNNAMED
                                --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                                --add-opens java.base/java.io=ALL-UNNAMED
                                --add-opens java.base/java.nio=ALL-UNNAMED
                                --add-opens java.base/java.time=ALL-UNNAMED
                                --add-opens java.logging/java.util.logging=ALL-UNNAMED
                                --add-opens java.base/sun.nio.ch=ALL-UNNAMED
                                --add-opens java.base/sun.security.util=ALL-UNNAMED
                                --add-opens java.base/java.net=ALL-UNNAMED
                                --add-opens java.base/java.security=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-opens java.base/java.lang.invoke=ALL-UNNAMED
                                --add-opens java.base/java.text=ALL-UNNAMED
                                --add-opens java.base/java.util.stream=ALL-UNNAMED
                                --add-opens java.base/java.util.regex=ALL-UNNAMED
                                --add-opens java.base/java.math=ALL-UNNAMED
                                --add-opens java.instrument/java.lang.instrument=ALL-UNNAMED
                                --add-opens java.instrument/sun.instrument=ALL-UNNAMED
                                --add-exports java.base/sun.nio.ch=ALL-UNNAMED
                                --add-exports java.base/sun.security.util=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-exports java.instrument/sun.instrument=ALL-UNNAMED
                                -Djdk.instrument.traceUsage=false
                                -XX:+EnableDynamicAgentLoading
                                -Dnet.bytebuddy.experimental=true
                            </argLine>
                            <includes>
                                <include>**/*Test.java</include>
                                <include>**/*Tests.java</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        
        <!-- Java 11构建 - 在Java 21环境下运行时需要额外的模块访问配置 -->
        <profile>
            <id>java11</id>
            <properties>
                <java.target.version>11</java.target.version>
                <java.release.version>11</java.release.version>
                <jar.classifier.suffix>-java11</jar.classifier.suffix>
                <jar.classifier>java11</jar.classifier>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- 在Java 21环境下运行Java 11构建时需要的模块访问权限 -->
                            <argLine>
                                --add-opens java.base/java.lang=ALL-UNNAMED
                                --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                                --add-opens java.base/java.util=ALL-UNNAMED
                                --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                                --add-opens java.base/java.io=ALL-UNNAMED
                                --add-opens java.base/java.nio=ALL-UNNAMED
                                --add-opens java.base/java.time=ALL-UNNAMED
                                --add-opens java.logging/java.util.logging=ALL-UNNAMED
                                --add-opens java.base/sun.nio.ch=ALL-UNNAMED
                                --add-opens java.base/sun.security.util=ALL-UNNAMED
                                --add-opens java.base/java.net=ALL-UNNAMED
                                --add-opens java.base/java.security=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-opens java.base/java.lang.invoke=ALL-UNNAMED
                                --add-opens java.base/java.text=ALL-UNNAMED
                                --add-opens java.base/java.util.stream=ALL-UNNAMED
                                --add-opens java.base/java.util.regex=ALL-UNNAMED
                                --add-opens java.base/java.math=ALL-UNNAMED
                                --add-opens java.instrument/java.lang.instrument=ALL-UNNAMED
                                --add-opens java.instrument/sun.instrument=ALL-UNNAMED
                                --add-exports java.base/sun.nio.ch=ALL-UNNAMED
                                --add-exports java.base/sun.security.util=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-exports java.instrument/sun.instrument=ALL-UNNAMED
                                -Djdk.instrument.traceUsage=false
                                -XX:+EnableDynamicAgentLoading
                                -Dnet.bytebuddy.experimental=true
                            </argLine>
                            <includes>
                                <include>**/*Test.java</include>
                                <include>**/*Tests.java</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        
        <!-- Java 17构建 - 在Java 21环境下运行时需要额外的模块访问配置 -->
        <profile>
            <id>java17</id>
            <properties>
                <java.target.version>17</java.target.version>
                <java.release.version>17</java.release.version>
                <jar.classifier.suffix>-java17</jar.classifier.suffix>
                <jar.classifier>java17</jar.classifier>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- 在Java 21环境下运行Java 17构建时需要的完整模块访问权限 -->
                            <argLine>
                                --add-opens java.base/java.lang=ALL-UNNAMED
                                --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                                --add-opens java.base/java.util=ALL-UNNAMED
                                --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                                --add-opens java.base/java.io=ALL-UNNAMED
                                --add-opens java.base/java.nio=ALL-UNNAMED
                                --add-opens java.base/java.time=ALL-UNNAMED
                                --add-opens java.logging/java.util.logging=ALL-UNNAMED
                                --add-opens java.base/sun.nio.ch=ALL-UNNAMED
                                --add-opens java.base/sun.security.util=ALL-UNNAMED
                                --add-opens java.base/java.net=ALL-UNNAMED
                                --add-opens java.base/java.security=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-opens java.base/java.lang.invoke=ALL-UNNAMED
                                --add-opens java.base/java.text=ALL-UNNAMED
                                --add-opens java.base/java.util.stream=ALL-UNNAMED
                                --add-opens java.base/java.util.regex=ALL-UNNAMED
                                --add-opens java.base/java.math=ALL-UNNAMED
                                --add-opens java.instrument/java.lang.instrument=ALL-UNNAMED
                                --add-opens java.instrument/sun.instrument=ALL-UNNAMED
                                --add-exports java.base/sun.nio.ch=ALL-UNNAMED
                                --add-exports java.base/sun.security.util=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-exports java.instrument/sun.instrument=ALL-UNNAMED
                                -Djdk.instrument.traceUsage=false
                                -XX:+EnableDynamicAgentLoading
                                -Dnet.bytebuddy.experimental=true
                            </argLine>
                            <includes>
                                <include>**/*Test.java</include>
                                <include>**/*Tests.java</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        
        <!-- Java 21特殊配置 - 解决反射访问问题 -->
        <profile>
            <id>java21</id>
            <properties>
                <java.target.version>21</java.target.version>
                <java.release.version>21</java.release.version>
                <jar.classifier.suffix>-java21</jar.classifier.suffix>
                <jar.classifier>java21</jar.classifier>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <argLine>
                                --add-opens java.base/java.lang=ALL-UNNAMED
                                --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                                --add-opens java.base/java.util=ALL-UNNAMED
                                --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                                --add-opens java.base/java.io=ALL-UNNAMED
                                --add-opens java.base/java.nio=ALL-UNNAMED
                                --add-opens java.base/java.time=ALL-UNNAMED
                                --add-opens java.logging/java.util.logging=ALL-UNNAMED
                                --add-opens java.base/sun.nio.ch=ALL-UNNAMED
                                --add-opens java.base/sun.security.util=ALL-UNNAMED
                                --add-opens java.base/java.net=ALL-UNNAMED
                                --add-opens java.base/java.security=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-opens java.base/java.lang.invoke=ALL-UNNAMED
                                --add-opens java.base/java.text=ALL-UNNAMED
                                --add-opens java.base/java.util.stream=ALL-UNNAMED
                                --add-opens java.base/java.util.regex=ALL-UNNAMED
                                --add-opens java.base/java.math=ALL-UNNAMED
                                --add-opens java.instrument/java.lang.instrument=ALL-UNNAMED
                                --add-opens java.instrument/sun.instrument=ALL-UNNAMED
                                --add-exports java.base/sun.nio.ch=ALL-UNNAMED
                                --add-exports java.base/sun.security.util=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED
                                --add-exports java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                                --add-exports java.instrument/sun.instrument=ALL-UNNAMED
                                -Djdk.instrument.traceUsage=false
                                -XX:+EnableDynamicAgentLoading
                                -Dnet.bytebuddy.experimental=true
                            </argLine>
                            <includes>
                                <include>**/*Test.java</include>
                                <include>**/*Tests.java</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project> 