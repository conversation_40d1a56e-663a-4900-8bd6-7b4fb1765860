package com.sdesrd.filetransfer.server;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;

/**
 * 秒传API改进测试
 * 验证FileUploadInitResponse新增字段的功能
 */
public class FastUploadApiImprovementTest {

    @Test
    @DisplayName("FileUploadInitResponse新增字段测试")
    void testFileUploadInitResponseNewFields() {
        // 创建响应对象
        FileUploadInitResponse response = new FileUploadInitResponse();
        
        // 设置基础字段
        response.setTransferId("test-transfer-id");
        response.setFileId("01JYRCQZJAYM87T42CSJZE6VEB");
        response.setFileName("test-file.txt");
        response.setFastUpload(true);
        response.setChunkSize(2048L);
        response.setTotalChunks(5);
        
        // 设置新增的字段（秒传时的完整信息）
        response.setRelativePath("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt");
        response.setFileMd5("3699b11b956ae9471fb8961640b55c1c");
        response.setCompleteTime("2024-12-27T10:00:00Z");
        response.setFileSize(10240L);
        response.setTransferDuration(150L);
        
        // 验证基础字段
        assertEquals("test-transfer-id", response.getTransferId());
        assertEquals("01JYRCQZJAYM87T42CSJZE6VEB", response.getFileId());
        assertEquals("test-file.txt", response.getFileName());
        assertTrue(response.getFastUpload());
        assertEquals(2048L, response.getChunkSize());
        assertEquals(5, response.getTotalChunks());
        
        // 验证新增字段
        assertEquals("202506/01JYRCQZJAYM87T42CSJZE6VEB/3699b11b956ae9471fb8961640b55c1c.txt", 
                    response.getRelativePath());
        assertEquals("3699b11b956ae9471fb8961640b55c1c", response.getFileMd5());
        assertEquals("2024-12-27T10:00:00Z", response.getCompleteTime());
        assertEquals(10240L, response.getFileSize());
        assertEquals(150L, response.getTransferDuration());
    }
    
    @Test
    @DisplayName("普通上传时新增字段为null测试")
    void testNormalUploadNewFieldsAreNull() {
        // 创建普通上传的响应对象
        FileUploadInitResponse response = new FileUploadInitResponse();
        
        // 设置基础字段（普通上传）
        response.setTransferId("normal-transfer-id");
        response.setFileId("01JYRCQZJAYM87T42CSJZE6VEB");
        response.setFileName("normal-file.txt");
        response.setFastUpload(false);
        response.setChunkSize(2048L);
        response.setTotalChunks(5);
        
        // 新增字段应该为null（普通上传时）
        response.setRelativePath(null);
        response.setFileMd5(null);
        response.setCompleteTime(null);
        response.setFileSize(null);
        response.setTransferDuration(null);
        
        // 验证基础字段
        assertEquals("normal-transfer-id", response.getTransferId());
        assertEquals("01JYRCQZJAYM87T42CSJZE6VEB", response.getFileId());
        assertEquals("normal-file.txt", response.getFileName());
        assertFalse(response.getFastUpload());
        assertEquals(2048L, response.getChunkSize());
        assertEquals(5, response.getTotalChunks());
        
        // 验证新增字段为null
        assertNull(response.getRelativePath());
        assertNull(response.getFileMd5());
        assertNull(response.getCompleteTime());
        assertNull(response.getFileSize());
        assertNull(response.getTransferDuration());
    }
}
