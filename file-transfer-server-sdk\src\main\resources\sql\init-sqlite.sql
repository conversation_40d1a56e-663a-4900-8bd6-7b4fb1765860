-- 文件传输SDK - SQLite数据库初始化脚本
-- 版本: 1.0.0 - 客户端指定fileId版本

-- 文件传输记录表
CREATE TABLE IF NOT EXISTS file_transfer_record (
    id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    file_name TEXT NOT NULL,
    original_file_name TEXT,
    file_size INTEGER NOT NULL,
    file_path TEXT,
    transferred_size INTEGER NOT NULL DEFAULT 0,
    total_chunks INTEGER NOT NULL DEFAULT 0,
    completed_chunks INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 0,
    client_ip TEXT,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    complete_time TEXT,
    fail_reason TEXT,
    file_type TEXT,
    ext_info TEXT
);

-- 创建索引和约束
CREATE INDEX IF NOT EXISTS idx_file_id ON file_transfer_record(file_id);
CREATE INDEX IF NOT EXISTS idx_status ON file_transfer_record(status);
CREATE INDEX IF NOT EXISTS idx_client_ip ON file_transfer_record(client_ip);
CREATE INDEX IF NOT EXISTS idx_create_time ON file_transfer_record(create_time);
CREATE INDEX IF NOT EXISTS idx_update_time ON file_transfer_record(update_time);
CREATE INDEX IF NOT EXISTS idx_complete_time ON file_transfer_record(complete_time);

-- 为已完成状态的文件添加fileId唯一性约束（客户端指定fileId模式）
-- 注意：SQLite不支持条件唯一约束，这里通过应用层逻辑保证
-- 在应用层确保：status=2（已完成）的记录中fileId唯一
CREATE INDEX IF NOT EXISTS idx_file_id_status ON file_transfer_record(file_id, status);

-- 文件分块记录表
CREATE TABLE IF NOT EXISTS file_chunk_record (
    id TEXT PRIMARY KEY,
    transfer_id TEXT NOT NULL,
    file_id TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    chunk_size INTEGER,
    chunk_offset INTEGER NOT NULL,
    chunk_path TEXT,
    chunk_md5 TEXT,
    status INTEGER NOT NULL DEFAULT 0,
    retry_count INTEGER NOT NULL DEFAULT 0,
    create_time TEXT NOT NULL,
    complete_time TEXT,
    fail_reason TEXT
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_transfer_chunk ON file_chunk_record(transfer_id, chunk_index);
CREATE INDEX IF NOT EXISTS idx_transfer_id ON file_chunk_record(transfer_id);
CREATE INDEX IF NOT EXISTS idx_file_id_chunk ON file_chunk_record(file_id);
CREATE INDEX IF NOT EXISTS idx_status_chunk ON file_chunk_record(status);
CREATE INDEX IF NOT EXISTS idx_chunk_index ON file_chunk_record(chunk_index);
CREATE INDEX IF NOT EXISTS idx_create_time_chunk ON file_chunk_record(create_time);

-- 插入版本信息（用于数据库版本管理）
CREATE TABLE IF NOT EXISTS db_version (
    version TEXT PRIMARY KEY,
    description TEXT,
    update_time TEXT
);

INSERT OR REPLACE INTO db_version (version, description, update_time)
VALUES ('1.0.0', '客户端指定fileId版本：支持客户端指定ULID格式的fileId', datetime('now'));

-- 状态说明注释
/*
file_transfer_record.status:
0 - 待传输
1 - 传输中
2 - 传输完成
3 - 传输失败
4 - 已暂停

file_chunk_record.status:
0 - 待传输
1 - 传输完成
2 - 传输失败
*/ 